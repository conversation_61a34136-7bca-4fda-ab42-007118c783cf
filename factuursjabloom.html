<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Factuur Template</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @page {
            size: A4;
            margin: 5mm;
        }
        body {
            font-family: 'Inter', sans-serif;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            margin: 0;
            padding: 0;
        }
        .invoice-container {
            width: 210mm;
            min-height: 297mm;
            max-height: 297mm;
            margin: 0 auto;
            padding-bottom: 20mm; /* Ruimte voor footer */
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 5px 10px -5px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
            font-size: 12px;
        }
        .header-gradient {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        }
        .service-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        .plumber {
            background-color: #60a5fa;
            color: #1e40af;
        }
        .electrician {
            background-color: #fbbf24;
            color: #92400e;
        }
        .handyman {
            background-color: #34d399;
            color: #065f46;
        }
        .divider {
            border-top: 1px dashed #d1d5db;
        }
        .total-row {
            background-color: #f3f4f6;
        }
        .qr-container {
            width: 100px;
            height: 100px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto;
        }
        .qr-container::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid rgba(37, 99, 235, 0.1);
            border-radius: 12px;
            z-index: 1;
        }
        .qr-container::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid rgba(37, 99, 235, 0.2);
            border-radius: 8px;
            z-index: 2;
        }
        .qr-code-wrapper {
            position: relative;
            z-index: 3;
            padding: 8px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .payment-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .payment-method {
            display: flex;
            align-items: center;
            padding: 6px 10px;
            background-color: #f8fafc;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }
        .payment-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #2563eb;
            color: white;
            border-radius: 50%;
            margin-right: 8px;
        }
        .totals-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .compact-row {
            padding-top: 3px;
            padding-bottom: 3px;
        }
        .ideal-logo {
            color: #0a0a9e;
            font-size: 1.5rem;
            margin-right: 5px;
        }
        .auto-scale {
            transform-origin: top left;
            transform: scale(calc(1 - var(--scale-factor, 0)));
        }
        .content-wrapper {
            max-height: 240mm;
            overflow: hidden;
        }
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            .invoice-container {
                box-shadow: none;
                padding-bottom: 15mm;
            }
            .no-print {
                display: none !important;
            }
        }
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) { 
            .invoice-container {
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 5px 10px -5px rgba(0, 0, 0, 0.04);
            }
            
            .table-row {
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            }
            
            /* Scherpere randen en tekst */
            * {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }
        
        /* Zorg dat alles past op A4 */
        @page {
            size: A4;
            margin: 5mm;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                width: 210mm;
                height: 297mm;
                page-break-after: always;
                box-shadow: none !important;
            }
        }
        
        /* Tekstschaduwen voor alle inhoudselementen */
        .text-shadow-sm {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        /* Toepassen van tekstschaduw op alle relevante tekstelementen */
        h1, h2, h3, p, span, td, th, .font-semibold, .font-bold, .font-medium {
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        }
        
        /* Sterkere schaduw voor belangrijke bedragen */
        .grand-total span, .total-row td:last-child, .font-bold {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
        }
        
        /* Subtiele schaduw voor normale tekst */
        .text-gray-600, .text-gray-700, .text-gray-800 {
            text-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body class="bg-gray-100 print:bg-white">
    <div class="invoice-container bg-white mx-auto shadow-lg" id="invoice" style="max-width: 210mm; min-height: 297mm; max-height: 297mm; margin: 0 auto;">
        <!-- Header (extra compact) -->
        <div class="header-gradient px-4 py-1.5 text-white" style="background-image: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div class="flex justify-between items-center">
                <div class="flex flex-col">
                    <div class="relative">
                        <h1 class="text-lg font-bold tracking-wider relative z-10 uppercase bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100" style="text-shadow: 0 2px 4px rgba(0,0,0,0.2); letter-spacing: 0.1em; line-height: 1.2;">FACTUUR</h1>
                    </div>
                    <div class="flex flex-wrap items-center text-xxs text-blue-100 mt-0.5">
                        <div class="flex items-center mr-2 bg-blue-700 bg-opacity-20 px-1.5 py-0 rounded-full border border-blue-400 border-opacity-20">
                            <i class="fas fa-phone-alt mr-1 text-xs text-blue-200"></i>
                            <span class="font-bold text-white">020-1234567</span>
                        </div>
                        <div class="flex items-center mr-2 bg-blue-700 bg-opacity-20 px-1.5 py-0 rounded-full border border-blue-400 border-opacity-20">
                            <i class="fas fa-envelope mr-1 text-xs text-blue-200"></i>
                            <span class="font-bold text-white"><EMAIL></span>
                        </div>
                        <div class="flex items-center bg-blue-700 bg-opacity-20 px-1.5 py-0 rounded-full border border-blue-400 border-opacity-20">
                            <i class="fas fa-globe mr-1 text-xs text-blue-200"></i>
                            <span class="font-bold text-white">www.klusexperts.nl</span>
                        </div>
                    </div>
                </div>
                <!-- Logo direct op de header achtergrond -->
                <div class="w-28 h-auto flex items-center justify-center py-1 px-1">
                    <!-- Bedrijfslogo afbeelding zonder aparte achtergrond -->
                    <img 
                        src="WhatsApp Image 2024-03-09 at 21,11,33-Picsart-BackgroundRemover.png" 
                        alt="Bedrijfslogo" 
                        class="max-w-full max-h-full object-contain drop-shadow-lg" 
                        style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.6)) drop-shadow(0 0 2px rgba(0,0,0,0.3)) brightness(1.2) contrast(1.2); width: 95%;"
                    >
                </div>
            </div>
        </div>

        <!-- Company and Client Info -->
        <div class="px-4 py-3 grid grid-cols-3 gap-6 text-xs">
            <!-- Klantgegevens links -->
            <div class="col-span-1">
                <h2 class="text-sm font-semibold text-gray-800 mb-1">Klantgegevens</h2>
                <p class="font-bold text-gray-900">Jansen Bouw BV</p>
                <p class="text-gray-600">t.a.v. Dhr. P. Jansen</p>
                <p class="text-gray-600">Prinsengracht 456</p>
                <p class="text-gray-600">1016 HT Amsterdam</p>
                <p class="text-gray-600">KVK: 87654321</p>
                <p class="text-gray-600">BTW: NL987654321B02</p>
                <p class="text-gray-600">Tel: 020-7654321</p>
            </div>
            
            <!-- Bedrijfsgegevens midden -->
            <div class="col-span-1">
                <h2 class="text-sm font-semibold text-gray-800 mb-1">Bedrijfsgegevens</h2>
                <p class="font-bold text-gray-900">KlusExperts BV</p>
                <p class="text-gray-600">t.n.v. J. de Vries</p>
                <p class="text-gray-600">Ambachtsweg 123</p>
                <p class="text-gray-600">1043 AC Amsterdam</p>
                <p class="text-gray-600">KVK: 12345678</p>
                <p class="text-gray-600">BTW: NL123456789B01</p>
                <p class="text-gray-600">IBAN: NL12 INGB 0000 1234</p>
            </div>
            
            <!-- Factuurdetails rechts -->
            <div class="bg-gray-50 p-3 rounded">
                <div class="grid grid-cols-2 gap-2">
                    <div>
                        <p class="text-gray-500 font-medium">Factuurnummer</p>
                        <p class="font-bold text-gray-900">2023-00124</p>
                    </div>
                    <div>
                        <p class="text-gray-500 font-medium">Factuurdatum</p>
                        <p class="font-bold text-gray-900">15-11-2023</p>
                    </div>
                    <div>
                        <p class="text-gray-500 font-medium">Vervaldatum</p>
                        <p class="font-bold text-gray-900">30-11-2023</p>
                    </div>
                    <div>
                        <p class="text-gray-500 font-medium">Uitgevoerd op</p>
                        <p class="font-bold text-gray-900">10-11-2023</p>
                    </div>
                    <div>
                        <p class="text-gray-500 font-medium">Service</p>
                        <p class="font-bold"><span class="service-badge header-gradient text-white">Loodgieter</span></p>
                    </div>
                    <div>
                        <p class="text-gray-500 font-medium">Betaaltermijn</p>
                        <p class="font-bold text-gray-900">15 dagen</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content-wrapper px-4">
            <!-- Services Table -->
            <div class="px-4 py-2 bg-blue-50 rounded-md">
                <table class="w-full text-xs">
                    <thead>
                        <tr class="text-left text-white header-gradient rounded-md">
                            <th class="py-2 px-2 font-semibold">Aantal</th>
                            <th class="py-2 px-2 font-semibold">Beschrijving</th>
                            <th class="py-2 px-2 font-semibold">Uur</th>
                            <th class="py-2 px-2 font-semibold">Tarief</th>
                            <th class="py-2 px-2 font-semibold">BTW</th>
                            <th class="py-2 px-2 font-semibold text-right">Totaal</th>
                        </tr>
                    </thead>
                    <tbody id="services-body">
                        <!-- Items will be added here dynamically -->
                    </tbody>
                </table>
            </div>

            <!-- Additional Costs -->
            <div class="px-3 py-2 mt-2 bg-blue-50 rounded-md">
                <h3 class="text-xs font-semibold text-gray-700 mb-1.5 flex items-center">
                    <i class="fas fa-receipt text-blue-600 mr-1.5"></i>
                    Extra kosten
                </h3>
                <div class="grid grid-cols-2 gap-2">
                    <div class="flex justify-between items-center bg-white p-1.5 rounded shadow-sm">
                        <div class="flex items-center">
                            <i class="fas fa-truck text-blue-500 mr-1.5 text-xs"></i>
                            <span class="text-gray-700 font-medium text-xxs">Voorrijkosten</span>
                        </div>
                        <span class="font-semibold text-gray-800 text-xs">€ 25,00</span>
                    </div>
                    <div class="flex justify-between items-center bg-white p-1.5 rounded shadow-sm">
                        <div class="flex items-center">
                            <i class="fas fa-parking text-blue-500 mr-1.5 text-xs"></i>
                            <span class="text-gray-700 font-medium text-xxs">Parkeerkosten</span>
                        </div>
                        <span class="font-semibold text-gray-800 text-xs">€ 7,50</span>
                    </div>
                    <div class="flex justify-between items-center bg-white p-1.5 rounded shadow-sm">
                        <div class="flex items-center">
                            <i class="fas fa-bolt text-blue-500 mr-1.5 text-xs"></i>
                            <span class="text-gray-700 font-medium text-xxs">Spoedservice</span>
                        </div>
                        <span class="font-semibold text-gray-800 text-xs">€ 0,00</span>
                    </div>
                    <div class="flex justify-between items-center bg-white p-1.5 rounded shadow-sm">
                        <div class="flex items-center">
                            <i class="fas fa-tools text-blue-500 mr-1.5 text-xs"></i>
                            <span class="text-gray-700 font-medium text-xxs">Materiaalkosten</span>
                        </div>
                        <span class="font-semibold text-gray-800 text-xs">€ 48,75</span>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500 bg-white p-2 rounded-md">
                    <p class="flex items-center"><i class="fas fa-info-circle text-blue-400 mr-1"></i> Materiaal details: Afvoerbuis 40mm (2m), pakkingen set (3 stuks), slangklemmen (4 stuks), montagekit, afdichtingsring</p>
                </div>
            </div>

            <!-- Totals -->
            <div class="px-4 py-4 mt-4">
                <div class="grid grid-cols-3 gap-4">
                    <div class="payment-card">
                        <h3 class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-credit-card text-blue-600 mr-2"></i>
                            Betalingsgegevens
                        </h3>
                        <div class="text-xs">
                            <div class="flex items-center mb-2 bg-blue-50 p-2 rounded-md">
                                <div class="payment-icon">
                                    <i class="fas fa-university text-xs"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-xxs">IBAN</p>
                                    <p class="font-bold text-gray-900">NL12 INGB 0000 1234</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-1">t.n.v. KlusExperts BV</p>
                            <p class="text-gray-600 mb-2">O.v.v. factuurnummer 2023-00124</p>
                            
                            <div class="payment-method inline-flex items-center mt-2">
                                <i class="ideal-logo fab fa-ideal"></i>
                                <span class="font-medium">iDEAL betaling mogelijk</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- QR Code in the middle -->
                    <div class="flex flex-col items-center justify-center">
                        <div class="qr-code-container relative bg-gradient-to-br from-blue-50 to-white p-2 rounded-xl shadow-md" style="width: 150px; height: 150px;">
                            <!-- Modern QR code met afgeronde hoeken en gradient effecten -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="130" height="130" viewBox="0 0 150 150" fill="none" class="drop-shadow-md">
                                <!-- Verbeterde QR-code achtergrond -->
                                <rect width="150" height="150" fill="white" rx="10" style="filter: drop-shadow(0px 1px 2px rgba(0,0,0,0.2));"></rect>
                                
                                <!-- QR-code patroon (zwart-wit zoals in de afbeelding, maar vervaagd) -->
                                <g fill="black" opacity="0.7">
                                    <!-- Positioneringsblokken (hoeken) met verbeterde vormgeving -->
                                    <rect x="15" y="15" width="25" height="25" rx="2"></rect>
                                    <rect x="20" y="20" width="15" height="15" rx="1" fill="white"></rect>
                                    <rect x="25" y="25" width="5" height="5" rx="0.5"></rect>
                                    
                                    <rect x="110" y="15" width="25" height="25" rx="2"></rect>
                                    <rect x="115" y="20" width="15" height="15" rx="1" fill="white"></rect>
                                    <rect x="120" y="25" width="5" height="5" rx="0.5"></rect>
                                    
                                    <rect x="15" y="110" width="25" height="25" rx="2"></rect>
                                    <rect x="20" y="115" width="15" height="15" rx="1" fill="white"></rect>
                                    <rect x="25" y="120" width="5" height="5" rx="0.5"></rect>
                                    
                                    <!-- Toegevoegde vierde hoek (rechtsonder) voor volledige symmetrie -->
                                    <rect x="110" y="110" width="25" height="25" rx="2"></rect>
                                    <rect x="115" y="115" width="15" height="15" rx="1" fill="white"></rect>
                                    <rect x="120" y="120" width="5" height="5" rx="0.5"></rect>
                                    
                                    <!-- Data blokken (realistisch patroon) -->
                                    <!-- Rij 1 -->
                                    <rect x="45" y="15" width="5" height="5"></rect>
                                    <rect x="55" y="15" width="5" height="5"></rect>
                                    <rect x="60" y="15" width="5" height="5"></rect>
                                    <rect x="65" y="15" width="5" height="5"></rect>
                                    <rect x="70" y="15" width="5" height="5"></rect>
                                    <rect x="85" y="15" width="5" height="5"></rect>
                                    <rect x="95" y="15" width="5" height="5"></rect>
                                    
                                    <!-- Rij 2 -->
                                    <rect x="50" y="20" width="5" height="5"></rect>
                                    <rect x="75" y="20" width="5" height="5"></rect>
                                    <rect x="80" y="20" width="5" height="5"></rect>
                                    <rect x="90" y="20" width="5" height="5"></rect>
                                    <rect x="95" y="20" width="5" height="5"></rect>
                                    <rect x="100" y="20" width="5" height="5"></rect>
                                    
                                    <!-- Rij 3 -->
                                    <rect x="45" y="25" width="5" height="5"></rect>
                                    <rect x="50" y="25" width="5" height="5"></rect>
                                    <rect x="55" y="25" width="5" height="5"></rect>
                                    <rect x="65" y="25" width="5" height="5"></rect>
                                    <rect x="80" y="25" width="5" height="5"></rect>
                                    <rect x="95" y="25" width="5" height="5"></rect>
                                    <rect x="100" y="25" width="5" height="5"></rect>
                                    
                                    <!-- Rij 4 -->
                                    <rect x="45" y="30" width="5" height="5"></rect>
                                    <rect x="55" y="30" width="5" height="5"></rect>
                                    <rect x="65" y="30" width="5" height="5"></rect>
                                    <rect x="70" y="30" width="5" height="5"></rect>
                                    <rect x="75" y="30" width="5" height="5"></rect>
                                    <rect x="85" y="30" width="5" height="5"></rect>
                                    <rect x="95" y="30" width="5" height="5"></rect>
                                    
                                    <!-- Rij 5 -->
                                    <rect x="45" y="35" width="5" height="5"></rect>
                                    <rect x="55" y="35" width="5" height="5"></rect>
                                    <rect x="65" y="35" width="5" height="5"></rect>
                                    <rect x="75" y="35" width="5" height="5"></rect>
                                    <rect x="85" y="35" width="5" height="5"></rect>
                                    <rect x="95" y="35" width="5" height="5"></rect>
                                    
                                    <!-- Rij 6 -->
                                    <rect x="15" y="45" width="5" height="5"></rect>
                                    <rect x="25" y="45" width="5" height="5"></rect>
                                    <rect x="30" y="45" width="5" height="5"></rect>
                                    <rect x="35" y="45" width="5" height="5"></rect>
                                    <rect x="45" y="45" width="5" height="5"></rect>
                                    <rect x="55" y="45" width="5" height="5"></rect>
                                    <rect x="65" y="45" width="5" height="5"></rect>
                                    <rect x="75" y="45" width="5" height="5"></rect>
                                    <rect x="85" y="45" width="5" height="5"></rect>
                                    <rect x="95" y="45" width="5" height="5"></rect>
                                    <rect x="105" y="45" width="5" height="5"></rect>
                                    <rect x="115" y="45" width="5" height="5"></rect>
                                    <rect x="120" y="45" width="5" height="5"></rect>
                                    <rect x="125" y="45" width="5" height="5"></rect>
                                    <rect x="130" y="45" width="5" height="5"></rect>
                                    
                                    <!-- Meer QR-code patronen -->
                                    <rect x="45" y="50" width="5" height="5"></rect>
                                    <rect x="65" y="50" width="5" height="5"></rect>
                                    <rect x="85" y="50" width="5" height="5"></rect>
                                    <rect x="105" y="50" width="5" height="5"></rect>
                                    
                                    <rect x="15" y="55" width="5" height="5"></rect>
                                    <rect x="25" y="55" width="5" height="5"></rect>
                                    <rect x="35" y="55" width="5" height="5"></rect>
                                    <rect x="45" y="55" width="5" height="5"></rect>
                                    <rect x="55" y="55" width="5" height="5"></rect>
                                    <rect x="95" y="55" width="5" height="5"></rect>
                                    <rect x="105" y="55" width="5" height="5"></rect>
                                    <rect x="115" y="55" width="5" height="5"></rect>
                                    <rect x="125" y="55" width="5" height="5"></rect>
                                    <rect x="130" y="55" width="5" height="5"></rect>
                                    
                                    <rect x="20" y="60" width="5" height="5"></rect>
                                    <rect x="30" y="60" width="5" height="5"></rect>
                                    <rect x="40" y="60" width="5" height="5"></rect>
                                    <rect x="50" y="60" width="5" height="5"></rect>
                                    <rect x="100" y="60" width="5" height="5"></rect>
                                    <rect x="110" y="60" width="5" height="5"></rect>
                                    <rect x="120" y="60" width="5" height="5"></rect>
                                    <rect x="130" y="60" width="5" height="5"></rect>
                                    
                                    <rect x="15" y="65" width="5" height="5"></rect>
                                    <rect x="25" y="65" width="5" height="5"></rect>
                                    <rect x="35" y="65" width="5" height="5"></rect>
                                    <rect x="45" y="65" width="5" height="5"></rect>
                                    <rect x="105" y="65" width="5" height="5"></rect>
                                    <rect x="115" y="65" width="5" height="5"></rect>
                                    <rect x="125" y="65" width="5" height="5"></rect>
                                    <rect x="135" y="65" width="5" height="5"></rect>
                                    
                                    <rect x="15" y="85" width="5" height="5"></rect>
                                    <rect x="25" y="85" width="5" height="5"></rect>
                                    <rect x="35" y="85" width="5" height="5"></rect>
                                    <rect x="45" y="85" width="5" height="5"></rect>
                                    <rect x="105" y="85" width="5" height="5"></rect>
                                    <rect x="115" y="85" width="5" height="5"></rect>
                                    <rect x="125" y="85" width="5" height="5"></rect>
                                    <rect x="135" y="85" width="5" height="5"></rect>
                                    
                                    <rect x="20" y="90" width="5" height="5"></rect>
                                    <rect x="30" y="90" width="5" height="5"></rect>
                                    <rect x="40" y="90" width="5" height="5"></rect>
                                    <rect x="50" y="90" width="5" height="5"></rect>
                                    <rect x="100" y="90" width="5" height="5"></rect>
                                    <rect x="110" y="90" width="5" height="5"></rect>
                                    <rect x="120" y="90" width="5" height="5"></rect>
                                    <rect x="130" y="90" width="5" height="5"></rect>
                                    
                                    <rect x="15" y="95" width="5" height="5"></rect>
                                    <rect x="25" y="95" width="5" height="5"></rect>
                                    <rect x="35" y="95" width="5" height="5"></rect>
                                    <rect x="45" y="95" width="5" height="5"></rect>
                                    <rect x="55" y="95" width="5" height="5"></rect>
                                    <rect x="95" y="95" width="5" height="5"></rect>
                                    <rect x="105" y="95" width="5" height="5"></rect>
                                    <rect x="115" y="95" width="5" height="5"></rect>
                                    <rect x="125" y="95" width="5" height="5"></rect>
                                    <rect x="130" y="95" width="5" height="5"></rect>
                                    
                                    <rect x="45" y="100" width="5" height="5"></rect>
                                    <rect x="65" y="100" width="5" height="5"></rect>
                                    <rect x="85" y="100" width="5" height="5"></rect>
                                    <rect x="105" y="100" width="5" height="5"></rect>
                                    
                                    <rect x="45" y="105" width="5" height="5"></rect>
                                    <rect x="55" y="105" width="5" height="5"></rect>
                                    <rect x="65" y="105" width="5" height="5"></rect>
                                    <rect x="75" y="105" width="5" height="5"></rect>
                                    <rect x="85" y="105" width="5" height="5"></rect>
                                    <rect x="95" y="105" width="5" height="5"></rect>
                                    
                                    <rect x="50" y="110" width="5" height="5"></rect>
                                    <rect x="60" y="110" width="5" height="5"></rect>
                                    <rect x="70" y="110" width="5" height="5"></rect>
                                    <rect x="80" y="110" width="5" height="5"></rect>
                                    <rect x="90" y="110" width="5" height="5"></rect>
                                    <rect x="100" y="110" width="5" height="5"></rect>
                                    
                                    <rect x="45" y="115" width="5" height="5"></rect>
                                    <rect x="55" y="115" width="5" height="5"></rect>
                                    <rect x="65" y="115" width="5" height="5"></rect>
                                    <rect x="75" y="115" width="5" height="5"></rect>
                                    <rect x="85" y="115" width="5" height="5"></rect>
                                    <rect x="95" y="115" width="5" height="5"></rect>
                                    
                                    <rect x="50" y="120" width="5" height="5"></rect>
                                    <rect x="60" y="120" width="5" height="5"></rect>
                                    <rect x="70" y="120" width="5" height="5"></rect>
                                    <rect x="80" y="120" width="5" height="5"></rect>
                                    <rect x="90" y="120" width="5" height="5"></rect>
                                    <rect x="100" y="120" width="5" height="5"></rect>
                                    
                                    <rect x="45" y="125" width="5" height="5"></rect>
                                    <rect x="55" y="125" width="5" height="5"></rect>
                                    <rect x="65" y="125" width="5" height="5"></rect>
                                    <rect x="75" y="125" width="5" height="5"></rect>
                                    <rect x="85" y="125" width="5" height="5"></rect>
                                    <rect x="95" y="125" width="5" height="5"></rect>
                                </g>
                                
                                <!-- Logo perfect gecentreerd in het midden van de QR-code -->
                                <g transform="translate(45, 45) scale(0.6)">
                                    <!-- iDEAL logo direct op QR-code, perfect gecentreerd -->
                                    <rect x="10" y="20" width="80" height="60" rx="30" fill="#e6007e" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.4));"></rect>
                                    <text x="25" y="60" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" style="filter: drop-shadow(0px 1px 2px rgba(0,0,0,0.4));">DEAL</text>
                                    <circle cx="15" cy="35" r="5" fill="white"></circle>
                                    <rect x="10" y="45" width="5" height="10" fill="white"></rect>
                                </g>
                            </svg>
                            
                            <!-- Scan text -->
                            <div class="absolute -bottom-6 left-0 right-0 text-center text-xs font-bold text-blue-800">Scan voor betaling</div>
                        </div>
                    </div>
                    
                    <div class="totals-card">
                        <h3 class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-calculator text-blue-600 mr-2"></i>
                            Totaaloverzicht
                        </h3>
                        <div class="text-xs">
                            <div class="flex justify-between py-1 compact-row">
                                <span class="text-gray-600">Subtotaal</span>
                                <span class="font-medium">€ 308,75</span>
                            </div>
                            <div class="flex justify-between py-1 compact-row border-b">
                                <span class="text-gray-600">BTW 21%</span>
                                <span class="font-medium">€ 64,84</span>
                            </div>
                            <div class="flex justify-between py-1 compact-row border-b">
                                <span class="text-gray-600">BTW 9%</span>
                                <span class="font-medium">€ 0,00</span>
                            </div>
                            <div class="flex justify-between py-1 compact-row border-b">
                                <span class="text-gray-600">Korting</span>
                                <span class="font-medium text-green-600">- € 15,00</span>
                            </div>
                            <div class="flex justify-between py-2 compact-row header-gradient text-white rounded-md px-2 mt-1 font-bold text-sm">
                                <span>Totaal incl. BTW</span>
                                <span>€ 358,59</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer - Compacter gemaakt -->
        <div class="px-3 py-1.5 bg-gradient-to-r from-gray-50 to-gray-100 text-xxs absolute bottom-0 w-full border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <!-- Garantie sectie -->
                <div class="flex items-start">
                    <i class="fas fa-shield-alt text-blue-500 mr-1.5 mt-0.5 text-xs"></i>
                    <div>
                        <p class="font-semibold text-gray-700 mb-0.5 text-xxs">Garantie</p>
                        <p class="text-gray-600 text-xxs leading-tight">Op alle uitgevoerde werkzaamheden geldt een garantieperiode van 3 maanden tot 5 augustus 2025. Materiaal valt onder fabrieksgarantie.</p>
                    </div>
                </div>
                
                <!-- Algemene voorwaarden sectie -->
                <div class="flex items-start">
                    <i class="fas fa-file-alt text-blue-500 mr-1.5 mt-0.5 text-xs"></i>
                    <div>
                        <p class="font-semibold text-gray-700 mb-0.5 text-xxs">Algemene voorwaarden</p>
                        <p class="text-gray-600 text-xxs leading-tight">Deze factuur valt onder onze algemene voorwaarden zoals gepubliceerd op www.klusexperts.nl/algemene-voorwaarden.</p>
                    </div>
                </div>
            </div>
            
            <!-- Bedankt en copyright -->
            <div class="mt-1 pt-1 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-500 order-2 md:order-1 mt-0.5 md:mt-0 text-xxs">Pagina 1/1</p>
                
                <div class="flex items-center justify-center order-1 md:order-2">
                    <i class="fas fa-heart text-red-400 mr-1 text-xs"></i>
                    <p class="text-gray-700 font-medium text-xxs">Bedankt voor uw vertrouwen in onze diensten</p>
                </div>
                
                <div class="order-3 text-gray-500 text-right mt-0.5 md:mt-0 text-xxs">
                    <p> 2025 KlusExperts BV</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data with 10 detailed items (compact version)
        const invoiceItems = [
            { 
                quantity: 1, 
                description: "Reparatie lekkende kraan keuken", 
                hours: 2.5, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Vervangen afvoerbuis wastafel", 
                hours: 1.5, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Reinigen doucheputje", 
                hours: 1.0, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 2, 
                description: "Monteren wandbeugels", 
                hours: 0.75, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Controle waterdruk", 
                hours: 0.5, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Vervangen toiletmechanisme", 
                hours: 1.25, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Reviseren afvoerputje", 
                hours: 2.0, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Installeren buitenkraan", 
                hours: 1.75, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Onderhoud cv-ketel", 
                hours: 1.0, 
                rate: 65, 
                vat: 21 
            },
            { 
                quantity: 1, 
                description: "Adviesgesprek waterinstallatie", 
                hours: 0.5, 
                rate: 65, 
                vat: 21 
            }
        ];

        // Calculate totals
        let subtotal = 0;
        let vat21 = 0;
        let vat9 = 0;
        const discount = 15;
        
        invoiceItems.forEach(item => {
            const itemTotal = item.quantity * item.hours * item.rate;
            subtotal += itemTotal;
            if (item.vat === 21) {
                vat21 += itemTotal * 0.21;
            } else if (item.vat === 9) {
                vat9 += itemTotal * 0.09;
            }
        });

        // Add fixed costs
        subtotal += 25 + 7.5 + 48.75;
        vat21 += (25 + 7.5 + 48.75) * 0.21;

        const total = subtotal + vat21 + vat9 - discount;

        // Render services table
        document.addEventListener('DOMContentLoaded', function() {
            const servicesBody = document.getElementById('services-body');
            
            if (servicesBody) {
                invoiceItems.forEach(item => {
                    const row = document.createElement('tr');
                    row.className = 'border-b compact-row';
                    const itemTotal = item.quantity * item.hours * item.rate;
                    
                    row.innerHTML = `
                        <td class="py-1 font-medium">${item.quantity}</td>
                        <td class="py-1 font-medium">${item.description}</td>
                        <td class="py-1 font-medium">${item.hours.toFixed(2)}</td>
                        <td class="py-1 font-medium">€ ${item.rate.toFixed(2)}</td>
                        <td class="py-1 font-medium">${item.vat}%</td>
                        <td class="py-1 text-right font-medium">€ ${itemTotal.toFixed(2)}</td>
                    `;
                    
                    servicesBody.appendChild(row);
                });
            }

            // Generate QR code
            const qrCode = document.getElementById('qrCode');
            if (qrCode) {
                // QR-code wordt nu als SVG vector getoond, dus we hoeven deze niet meer dynamisch te genereren
                // De onderstaande code is nu overbodig maar blijft behouden voor referentie
                /*
                const qrData = {
                    name: "KlusExperts BV",
                    iban: "NL12 INGB 0000 1234",
                    amount: total.toFixed(2),
                    reference: "2023-00124"
                };
                
                const qrString = `NL:B:${qrData.name}:${qrData.iban}:EUR:${qrData.amount}:${qrData.reference}:`;
                
                QRCode.toCanvas(qrCode, qrString, {
                    width: 70,
                    margin: 1,
                    color: {
                        dark: '#2563eb',
                        light: '#ffffff'
                    }
                }, function(error) {
                    if (error) console.error(error);
                });
                */
            }

            // Auto-scale logic to ensure everything fits on one page
            const invoice = document.getElementById('invoice');
            const contentWrapper = document.querySelector('.content-wrapper');
            
            if (invoice && contentWrapper) {
                // Check if content exceeds available space
                const checkFit = () => {
                    const availableHeight = 240; // mm
                    const contentHeight = contentWrapper.scrollHeight * 0.264583; // px to mm
                    
                    if (contentHeight > availableHeight) {
                        const scaleFactor = 1 - (contentHeight / availableHeight - 1) * 0.5;
                        invoice.style.setProperty('--scale-factor', Math.max(0.1, scaleFactor));
                        invoice.classList.add('auto-scale');
                    }
                };
                
                // Initial check
                checkFit();
                
                // Also check after a small delay to ensure all elements are rendered
                setTimeout(checkFit, 100);
            }
        });
        
        // Voeg high-resolution ondersteuning toe
        if (window.devicePixelRatio > 1) {
            document.querySelector('html').classList.add('high-dpi');
        }
    </script>
</body>
</html>
