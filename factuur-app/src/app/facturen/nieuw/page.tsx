'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useApp } from '@/contexts/AppContext';
import {
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  FileText,
  QrCode,
  Calculator
} from 'lucide-react';
import Link from 'next/link';

export default function NieuweFactuurPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    klanten,
    getKlant,
    createFactuur,
    generateFactuurnummer,
    bedrijfsInstellingen
  } = useApp();

  // Get URL parameters for pre-filling
  const klantIdParam = searchParams.get('klantId');
  const tijdregistratieIdParam = searchParams.get('tijdregistratieId');
  const urenParam = searchParams.get('uren');

  const [formData, setFormData] = useState({
    klantId: klantIdParam || '',
    factuurdatum: new Date().toISOString().split('T')[0],
    vervaldatum: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    uitvoerdatum: new Date().toISOString().split('T')[0],
    serviceType: 'loodgieter' as const,
    customService: '',
    betaaltermijn: bedrijfsInstellingen.standaardBetaaltermijn,
    qrCode: '',
    betaalLink: '',
  });

  const [factuurregels, setFactuurregels] = useState([
    {
      id: '1',
      aantal: 1,
      beschrijving: 'Werkzaamheden',
      detailBeschrijving: '',
      eenheid: 'uur',
      uren: parseFloat(urenParam || '0'),
      prijsPerUur: bedrijfsInstellingen.standaardUurtarief,
      totaalExclBtw: 0,
    }
  ]);

  const [extraKosten, setExtraKosten] = useState({
    voorrijkosten: bedrijfsInstellingen.standaardVoorrijkosten,
    spoedservice: bedrijfsInstellingen.standaardSpoedservice,
    parkeerkosten: 0,
    materiaalkosten: 0,
    materiaalDetails: '',
  });

  const [kortingPercentage, setKortingPercentage] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const serviceTypes = [
    { value: 'loodgieter', label: 'Loodgieter' },
    { value: 'elektricien', label: 'Elektricien' },
    { value: 'klusjesman', label: 'Klusjesman' },
    { value: 'timmerman', label: 'Timmerman' },
    { value: 'aannemer', label: 'Aannemer' },
    { value: 'custom', label: 'Anders...' },
  ];

  // Calculate totals
  const subtotaal = factuurregels.reduce((sum, regel) => sum + regel.totaalExclBtw, 0) +
                   Object.values(extraKosten).reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0);
  const kortingBedrag = (subtotaal * kortingPercentage) / 100;
  const subtotaalNaKorting = subtotaal - kortingBedrag;
  const btw21 = (subtotaalNaKorting * bedrijfsInstellingen.standaardBtw21) / 100;
  const btw9 = 0; // Voor eenvoud
  const totaalInclBtw = subtotaalNaKorting + btw21 + btw9;

  // Update factuurregels when values change
  useEffect(() => {
    setFactuurregels(prev => prev.map(regel => ({
      ...regel,
      totaalExclBtw: regel.aantal * regel.uren * regel.prijsPerUur
    })));
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleFactuurregelChange = (id: string, field: string, value: any) => {
    setFactuurregels(prev => prev.map(regel => {
      if (regel.id === id) {
        const updated = { ...regel, [field]: value };
        updated.totaalExclBtw = updated.aantal * updated.uren * updated.prijsPerUur;
        return updated;
      }
      return regel;
    }));
  };

  const addFactuurregel = () => {
    const newId = (factuurregels.length + 1).toString();
    setFactuurregels(prev => [...prev, {
      id: newId,
      aantal: 1,
      beschrijving: '',
      detailBeschrijving: '',
      eenheid: 'uur',
      uren: 0,
      prijsPerUur: bedrijfsInstellingen.standaardUurtarief,
      totaalExclBtw: 0,
    }]);
  };

  const removeFactuurregel = (id: string) => {
    if (factuurregels.length > 1) {
      setFactuurregels(prev => prev.filter(regel => regel.id !== id));
    }
  };

  const handleExtraKostenChange = (field: string, value: number | string) => {
    setExtraKosten(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.klantId) {
      alert('Selecteer een klant');
      return;
    }

    setIsSubmitting(true);

    try {
      const factuurData = {
        klantId: formData.klantId,
        factuurdatum: new Date(formData.factuurdatum),
        vervaldatum: new Date(formData.vervaldatum),
        uitvoerdatum: new Date(formData.uitvoerdatum),
        serviceType: formData.serviceType,
        customService: formData.customService,
        betaaltermijn: formData.betaaltermijn,
        factuurregels,
        extraKosten,
        kortingPercentage,
        kortingBedrag,
        subtotaal,
        btw21,
        btw9,
        totaalInclBtw,
        qrCode: formData.qrCode,
        betaalLink: formData.betaalLink,
        status: 'concept' as const,
        tijdregistratieId: tijdregistratieIdParam,
      };

      const factuurId = createFactuur(factuurData);
      router.push(`/facturen/${factuurId}`);
    } catch (error) {
      console.error('Error creating factuur:', error);
      alert('Er is een fout opgetreden bij het maken van de factuur');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedKlant = getKlant(formData.klantId);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/facturen"
          className="p-2 hover:bg-accent rounded-lg transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Nieuwe Factuur</h1>
          <p className="text-muted-foreground mt-1">
            Maak een professionele factuur
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Klant en Factuurgegevens */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-foreground mb-4">Factuurgegevens</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Klant *
              </label>
              <select
                name="klantId"
                value={formData.klantId}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="">Selecteer een klant...</option>
                {klanten.map((klant) => (
                  <option key={klant.id} value={klant.id}>
                    {klant.type === 'bedrijf' && klant.bedrijfsnaam
                      ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`
                      : `${klant.voornaam} ${klant.achternaam}`
                    }
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Factuurnummer
              </label>
              <input
                type="text"
                value={generateFactuurnummer()}
                disabled
                className="w-full px-3 py-2 border border-input rounded-lg bg-muted text-muted-foreground"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Factuurdatum *
              </label>
              <input
                type="date"
                name="factuurdatum"
                value={formData.factuurdatum}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Vervaldatum *
              </label>
              <input
                type="date"
                name="vervaldatum"
                value={formData.vervaldatum}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Uitvoerdatum *
              </label>
              <input
                type="date"
                name="uitvoerdatum"
                value={formData.uitvoerdatum}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Service Type
              </label>
              <select
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                {serviceTypes.map((service) => (
                  <option key={service.value} value={service.value}>
                    {service.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {formData.serviceType === 'custom' && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-foreground mb-2">
                Aangepaste Service
              </label>
              <input
                type="text"
                name="customService"
                value={formData.customService}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                placeholder="Beschrijf de service..."
              />
            </div>
          )}

          <div className="mt-4">
            <label className="block text-sm font-medium text-foreground mb-2">
              Betalingstermijn (dagen)
            </label>
            <select
              name="betaaltermijn"
              value={formData.betaaltermijn}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value={7}>7 dagen</option>
              <option value={14}>14 dagen</option>
              <option value={30}>30 dagen</option>
            </select>
          </div>
        </div>

        {/* Klantgegevens Preview */}
        {selectedKlant && (
          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-4">Klantgegevens</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Naam:</span>
                <div className="mt-1">
                  {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam
                    ? selectedKlant.bedrijfsnaam
                    : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`
                  }
                </div>
                {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam && (
                  <div className="text-muted-foreground">
                    Contactpersoon: {selectedKlant.voornaam} {selectedKlant.achternaam}
                  </div>
                )}
              </div>
              <div>
                <span className="font-medium">Adres:</span>
                <div className="mt-1">
                  {selectedKlant.adres} {selectedKlant.huisnummer}<br />
                  {selectedKlant.postcode} {selectedKlant.stad}
                </div>
              </div>
              <div>
                <span className="font-medium">Telefoon:</span>
                <div className="mt-1">{selectedKlant.telefoonnummer}</div>
              </div>
              <div>
                <span className="font-medium">E-mail:</span>
                <div className="mt-1">{selectedKlant.email}</div>
              </div>
            </div>
          </div>
        )}

        {/* Factuurregels */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-foreground">Factuurregels</h2>
            <button
              type="button"
              onClick={addFactuurregel}
              className="flex items-center space-x-2 bg-secondary text-secondary-foreground px-3 py-2 rounded-lg hover:bg-secondary/90 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Regel Toevoegen</span>
            </button>
          </div>

          <div className="space-y-4">
            {factuurregels.map((regel, index) => (
              <div key={regel.id} className="border border-border rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-1">
                      Aantal
                    </label>
                    <input
                      type="number"
                      value={regel.aantal}
                      onChange={(e) => handleFactuurregelChange(regel.id, 'aantal', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-foreground mb-1">
                      Beschrijving
                    </label>
                    <input
                      type="text"
                      value={regel.beschrijving}
                      onChange={(e) => handleFactuurregelChange(regel.id, 'beschrijving', e.target.value)}
                      className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                      placeholder="Beschrijving van werkzaamheden"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-1">
                      Uren
                    </label>
                    <input
                      type="number"
                      value={regel.uren}
                      onChange={(e) => handleFactuurregelChange(regel.id, 'uren', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-1">
                      Prijs/uur (€)
                    </label>
                    <input
                      type="number"
                      value={regel.prijsPerUur}
                      onChange={(e) => handleFactuurregelChange(regel.id, 'prijsPerUur', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    />
                  </div>

                  <div className="flex items-end">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Totaal (€)
                      </label>
                      <div className="px-3 py-2 border border-input rounded-lg bg-muted text-foreground font-medium">
                        €{regel.totaalExclBtw.toFixed(2)}
                      </div>
                    </div>
                    {factuurregels.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeFactuurregel(regel.id)}
                        className="ml-2 p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>

                <div className="mt-3">
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Detail Beschrijving (optioneel)
                  </label>
                  <textarea
                    value={regel.detailBeschrijving}
                    onChange={(e) => handleFactuurregelChange(regel.id, 'detailBeschrijving', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Gedetailleerde beschrijving van de werkzaamheden..."
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Extra Kosten */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-foreground mb-4">Extra Kosten</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Voorrijkosten (€)
              </label>
              <select
                value={extraKosten.voorrijkosten}
                onChange={(e) => handleExtraKostenChange('voorrijkosten', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value={0}>€0 - Geen voorrijkosten</option>
                <option value={25}>€25 - Standaard voorrijkosten</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Spoedservice (€)
              </label>
              <select
                value={extraKosten.spoedservice}
                onChange={(e) => handleExtraKostenChange('spoedservice', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value={0}>€0 - Geen spoedservice</option>
                <option value={25}>€25 - Spoedservice toeslag</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Parkeerkosten (€)
              </label>
              <input
                type="number"
                value={extraKosten.parkeerkosten}
                onChange={(e) => handleExtraKostenChange('parkeerkosten', parseFloat(e.target.value) || 0)}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Materiaalkosten (€)
              </label>
              <input
                type="number"
                value={extraKosten.materiaalkosten}
                onChange={(e) => handleExtraKostenChange('materiaalkosten', parseFloat(e.target.value) || 0)}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
          </div>

          {extraKosten.materiaalkosten > 0 && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-foreground mb-2">
                Materiaal Details
              </label>
              <textarea
                value={extraKosten.materiaalDetails}
                onChange={(e) => handleExtraKostenChange('materiaalDetails', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                placeholder="Beschrijf het gebruikte materiaal..."
              />
            </div>
          )}
        </div>

        {/* Korting en QR Code */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-4">Korting</h3>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Korting (%)
              </label>
              <select
                value={kortingPercentage}
                onChange={(e) => setKortingPercentage(parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value={0}>Geen korting</option>
                <option value={5}>5%</option>
                <option value={10}>10%</option>
                <option value={15}>15%</option>
                <option value={20}>20%</option>
                <option value={25}>25%</option>
              </select>
            </div>
          </div>

          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-4">Betaling</h3>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Betaal Link (optioneel)
              </label>
              <input
                type="url"
                name="betaalLink"
                value={formData.betaalLink}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                placeholder="https://betaal.link/..."
              />
            </div>
          </div>
        </div>

        {/* Totaal Overzicht */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-foreground mb-4">Totaal Overzicht</h2>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Subtotaal:</span>
              <span>€{subtotaal.toFixed(2)}</span>
            </div>

            {kortingBedrag > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Korting ({kortingPercentage}%):</span>
                <span>-€{kortingBedrag.toFixed(2)}</span>
              </div>
            )}

            <div className="flex justify-between">
              <span>BTW (21%):</span>
              <span>€{btw21.toFixed(2)}</span>
            </div>

            <div className="flex justify-between text-xl font-bold border-t border-border pt-3">
              <span>TOTAAL INCL. BTW:</span>
              <span className="text-green-600">€{totaalInclBtw.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Link
            href="/facturen"
            className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            Annuleren
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{isSubmitting ? 'Opslaan...' : 'Factuur Maken'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
