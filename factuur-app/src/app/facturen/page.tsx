'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { 
  FileText, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2, 
  Download,
  Send,
  Euro,
  Calendar,
  Filter,
  User,
  Building
} from 'lucide-react';
import Link from 'next/link';

export default function FacturenPage() {
  const { facturen, klanten, getKlant, deleteFactuur } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'concept' | 'verzonden' | 'betaald' | 'vervallen'>('all');
  const [sortBy, setSortBy] = useState<'datum' | 'nummer' | 'bedrag'>('datum');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Filter en sorteer facturen
  const filteredFacturen = facturen
    .filter(factuur => {
      const klant = getKlant(factuur.klantId);
      const matchesSearch = 
        factuur.factuurnummer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (klant && (
          klant.voornaam.toLowerCase().includes(searchTerm.toLowerCase()) ||
          klant.achternaam.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (klant.bedrijfsnaam && klant.bedrijfsnaam.toLowerCase().includes(searchTerm.toLowerCase()))
        ));
      
      const matchesStatus = statusFilter === 'all' || factuur.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'datum':
          comparison = new Date(a.factuurdatum).getTime() - new Date(b.factuurdatum).getTime();
          break;
        case 'nummer':
          comparison = a.factuurnummer.localeCompare(b.factuurnummer);
          break;
        case 'bedrag':
          comparison = a.totaalInclBtw - b.totaalInclBtw;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  // Statistieken
  const stats = {
    totaal: facturen.length,
    concept: facturen.filter(f => f.status === 'concept').length,
    verzonden: facturen.filter(f => f.status === 'verzonden').length,
    betaald: facturen.filter(f => f.status === 'betaald').length,
    vervallen: facturen.filter(f => f.status === 'vervallen').length,
    totaalBedrag: facturen.reduce((sum, f) => sum + f.totaalInclBtw, 0),
    openstaand: facturen.filter(f => f.status === 'verzonden').reduce((sum, f) => sum + f.totaalInclBtw, 0),
    betaaldBedrag: facturen.filter(f => f.status === 'betaald').reduce((sum, f) => sum + f.totaalInclBtw, 0),
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concept': return 'bg-gray-100 text-gray-800';
      case 'verzonden': return 'bg-blue-100 text-blue-800';
      case 'betaald': return 'bg-green-100 text-green-800';
      case 'vervallen': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'concept': return 'Concept';
      case 'verzonden': return 'Verzonden';
      case 'betaald': return 'Betaald';
      case 'vervallen': return 'Vervallen';
      default: return status;
    }
  };

  const handleDeleteFactuur = (id: string, factuurnummer: string) => {
    if (confirm(`Weet je zeker dat je factuur ${factuurnummer} wilt verwijderen?`)) {
      deleteFactuur(id);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Facturen</h1>
          <p className="text-muted-foreground mt-1">
            Beheer al uw facturen en betalingen
          </p>
        </div>
        <Link
          href="/facturen/nieuw"
          className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors mt-4 sm:mt-0"
        >
          <Plus className="h-4 w-4" />
          <span>Nieuwe Factuur</span>
        </Link>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totaal Facturen</p>
              <p className="text-2xl font-bold text-foreground">{stats.totaal}</p>
            </div>
            <FileText className="h-8 w-8 text-primary" />
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totaal Omzet</p>
              <p className="text-2xl font-bold text-green-600">€{stats.totaalBedrag.toFixed(2)}</p>
            </div>
            <Euro className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Openstaand</p>
              <p className="text-2xl font-bold text-orange-600">€{stats.openstaand.toFixed(2)}</p>
            </div>
            <Calendar className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Betaald</p>
              <p className="text-2xl font-bold text-green-600">€{stats.betaaldBedrag.toFixed(2)}</p>
            </div>
            <Euro className="h-8 w-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{stats.concept}</div>
          <div className="text-sm text-muted-foreground">Concept</div>
        </div>
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.verzonden}</div>
          <div className="text-sm text-muted-foreground">Verzonden</div>
        </div>
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{stats.betaald}</div>
          <div className="text-sm text-muted-foreground">Betaald</div>
        </div>
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{stats.vervallen}</div>
          <div className="text-sm text-muted-foreground">Vervallen</div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Zoek facturen..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="all">Alle statussen</option>
              <option value="concept">Concept</option>
              <option value="verzonden">Verzonden</option>
              <option value="betaald">Betaald</option>
              <option value="vervallen">Vervallen</option>
            </select>

            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="datum-desc">Nieuwste eerst</option>
              <option value="datum-asc">Oudste eerst</option>
              <option value="nummer-asc">Factuurnummer A-Z</option>
              <option value="nummer-desc">Factuurnummer Z-A</option>
              <option value="bedrag-desc">Hoogste bedrag</option>
              <option value="bedrag-asc">Laagste bedrag</option>
            </select>
          </div>
        </div>
      </div>

      {/* Facturen List */}
      <div className="bg-card border border-border rounded-lg shadow-sm">
        {filteredFacturen.length > 0 ? (
          <div className="divide-y divide-border">
            {filteredFacturen.map((factuur) => {
              const klant = getKlant(factuur.klantId);
              
              return (
                <div key={factuur.id} className="p-6 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg font-semibold text-foreground">
                            {factuur.factuurnummer}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(factuur.status)}`}>
                            {getStatusLabel(factuur.status)}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-foreground">Klant:</span>
                          <div className="flex items-center space-x-1 mt-1">
                            {klant?.type === 'bedrijf' ? (
                              <Building className="h-3 w-3 text-green-600" />
                            ) : (
                              <User className="h-3 w-3 text-blue-600" />
                            )}
                            <span className="text-muted-foreground">
                              {klant ? (
                                klant.type === 'bedrijf' && klant.bedrijfsnaam 
                                  ? klant.bedrijfsnaam 
                                  : `${klant.voornaam} ${klant.achternaam}`
                              ) : 'Onbekende klant'}
                            </span>
                          </div>
                        </div>

                        <div>
                          <span className="font-medium text-foreground">Factuurdatum:</span>
                          <div className="text-muted-foreground mt-1">
                            {new Date(factuur.factuurdatum).toLocaleDateString('nl-NL')}
                          </div>
                        </div>

                        <div>
                          <span className="font-medium text-foreground">Vervaldatum:</span>
                          <div className="text-muted-foreground mt-1">
                            {new Date(factuur.vervaldatum).toLocaleDateString('nl-NL')}
                          </div>
                        </div>

                        <div>
                          <span className="font-medium text-foreground">Bedrag:</span>
                          <div className="text-lg font-bold text-green-600 mt-1">
                            €{factuur.totaalInclBtw.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        href={`/facturen/${factuur.id}`}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        title="Bekijken"
                      >
                        <Eye className="h-4 w-4" />
                      </Link>
                      
                      <Link
                        href={`/facturen/${factuur.id}/bewerken`}
                        className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                        title="Bewerken"
                      >
                        <Edit className="h-4 w-4" />
                      </Link>

                      <button
                        className="p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors"
                        title="PDF Downloaden"
                      >
                        <Download className="h-4 w-4" />
                      </button>

                      {factuur.status === 'concept' && (
                        <button
                          className="p-2 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors"
                          title="Verzenden"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => handleDeleteFactuur(factuur.id, factuur.factuurnummer)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        title="Verwijderen"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">
              {searchTerm || statusFilter !== 'all' ? 'Geen facturen gevonden' : 'Nog geen facturen'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm || statusFilter !== 'all' 
                ? 'Probeer een andere zoekopdracht of filter.'
                : 'Maak uw eerste factuur om te beginnen.'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Link
                href="/facturen/nieuw"
                className="inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>Eerste Factuur Maken</span>
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-foreground mb-4">Snelle Acties</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Link
            href="/snelle-berekening"
            className="flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <Euro className="h-5 w-5 mr-2" />
            Snelle Berekening
          </Link>
          <Link
            href="/tijdregistratie"
            className="flex items-center justify-center p-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <Calendar className="h-5 w-5 mr-2" />
            Tijdregistratie
          </Link>
          <Link
            href="/klanten"
            className="flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <User className="h-5 w-5 mr-2" />
            Klanten Beheren
          </Link>
        </div>
      </div>
    </div>
  );
}
