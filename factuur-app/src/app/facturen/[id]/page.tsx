'use client';

import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useApp } from '@/contexts/AppContext';
import {
  ArrowLeft,
  Download,
  Share2,
  Edit,
  Send,
  Eye,
  FileText,
  Building,
  User,
  Calendar,
  Euro
} from 'lucide-react';
import Link from 'next/link';

export default function FactuurDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { getFactuur, getKlant, bedrijfsInstellingen } = useApp();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const factuurId = params.id as string;
  const factuur = getFactuur(factuurId);
  const klant = factuur ? getKlant(factuur.klantId) : null;

  if (!factuur) {
    return (
      <div className="text-center py-12">
        <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-foreground mb-2">
          Factuur niet gevonden
        </h3>
        <p className="text-muted-foreground mb-6">
          De opgevraagde factuur bestaat niet of is verwijderd.
        </p>
        <Link
          href="/facturen"
          className="inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Terug naar Facturen</span>
        </Link>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concept': return 'bg-gray-100 text-gray-800';
      case 'verzonden': return 'bg-blue-100 text-blue-800';
      case 'betaald': return 'bg-green-100 text-green-800';
      case 'vervallen': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'concept': return 'Concept';
      case 'verzonden': return 'Verzonden';
      case 'betaald': return 'Betaald';
      case 'vervallen': return 'Vervallen';
      default: return status;
    }
  };

  const handleGeneratePDF = async () => {
    setIsGeneratingPDF(true);
    try {
      // Import the professional PDF generator
      const { generateProfessionalPDF } = await import('@/utils/pdfGenerator');

      // Prepare factuur data
      const factuurData = {
        factuurnummer: factuur.factuurnummer,
        factuurdatum: new Date(factuur.factuurdatum),
        vervaldatum: new Date(factuur.vervaldatum),
        uitvoerdatum: new Date(factuur.uitvoerdatum),
        klant: klant,
        factuurregels: factuur.factuurregels,
        extraKosten: factuur.extraKosten,
        subtotaal: factuur.subtotaal,
        kortingBedrag: factuur.kortingBedrag,
        kortingPercentage: factuur.kortingPercentage,
        btw21: factuur.btw21,
        btw9: factuur.btw9,
        totaalInclBtw: factuur.totaalInclBtw,
        betaalLink: factuur.betaalLink,
        bedrijfsInstellingen: bedrijfsInstellingen
      };

      // Generate PDF
      const pdf = await generateProfessionalPDF(factuurData);

      // Save PDF
      pdf.save(`Factuur-${factuur.factuurnummer}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Er is een fout opgetreden bij het genereren van de PDF');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleShare = (method: 'whatsapp' | 'sms' | 'email') => {
    if (!klant) return;

    const message = `
Factuur ${factuur.factuurnummer}

Beste ${klant.voornaam} ${klant.achternaam},

Hierbij ontvangt u factuur ${factuur.factuurnummer} voor de uitgevoerde werkzaamheden.

Factuurbedrag: €${factuur.totaalInclBtw.toFixed(2)}
Vervaldatum: ${new Date(factuur.vervaldatum).toLocaleDateString('nl-NL')}

${factuur.betaalLink ? `Betalen kan via: ${factuur.betaalLink}` : ''}

Met vriendelijke groet,
${bedrijfsInstellingen.bedrijfsnaam}
    `.trim();

    switch (method) {
      case 'whatsapp':
        window.open(`https://wa.me/${klant.telefoonnummer.replace(/\D/g, '')}?text=${encodeURIComponent(message)}`, '_blank');
        break;
      case 'sms':
        window.open(`sms:${klant.telefoonnummer}?body=${encodeURIComponent(message)}`, '_blank');
        break;
      case 'email':
        window.open(`mailto:${klant.email}?subject=${encodeURIComponent(`Factuur ${factuur.factuurnummer}`)}&body=${encodeURIComponent(message)}`, '_blank');
        break;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/facturen"
            className="p-2 hover:bg-accent rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Factuur {factuur.factuurnummer}
            </h1>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(factuur.status)}`}>
                {getStatusLabel(factuur.status)}
              </span>
              <span className="text-muted-foreground">
                Aangemaakt op {new Date(factuur.createdAt).toLocaleDateString('nl-NL')}
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleGeneratePDF}
            disabled={isGeneratingPDF}
            className="flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
          >
            <Download className="h-4 w-4" />
            <span>{isGeneratingPDF ? 'Genereren...' : 'PDF Downloaden'}</span>
          </button>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => handleShare('whatsapp')}
              className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              title="Delen via WhatsApp"
            >
              <Share2 className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleShare('email')}
              className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              title="Delen via E-mail"
            >
              <Share2 className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleShare('sms')}
              className="p-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
              title="Delen via SMS"
            >
              <Share2 className="h-4 w-4" />
            </button>
          </div>

          <Link
            href={`/facturen/${factuur.id}/bewerken`}
            className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Edit className="h-4 w-4" />
            <span>Bewerken</span>
          </Link>
        </div>
      </div>

      {/* Factuur Content */}
      <div id="factuur-content" className="bg-white text-black p-8 rounded-lg shadow-lg max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div>
            {bedrijfsInstellingen.logo && (
              <img
                src={bedrijfsInstellingen.logo}
                alt="Logo"
                className="h-16 w-auto mb-4"
              />
            )}
            <h1 className="text-3xl font-bold text-blue-600 mb-2">FACTUUR</h1>
            <div className="text-lg font-semibold">{factuur.factuurnummer}</div>
          </div>

          <div className="text-right">
            <h2 className="text-xl font-bold mb-2">{bedrijfsInstellingen.bedrijfsnaam}</h2>
            <div className="text-sm space-y-1">
              <div>{bedrijfsInstellingen.adres}</div>
              <div>{bedrijfsInstellingen.postcode} {bedrijfsInstellingen.stad}</div>
              <div>{bedrijfsInstellingen.telefoonnummer}</div>
              <div>{bedrijfsInstellingen.email}</div>
              {bedrijfsInstellingen.website && <div>{bedrijfsInstellingen.website}</div>}
            </div>
          </div>
        </div>

        {/* Klant en Factuurgegevens */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-blue-600">Factuuradres</h3>
            {klant && (
              <div className="space-y-1">
                <div className="font-medium">
                  {klant.type === 'bedrijf' && klant.bedrijfsnaam
                    ? klant.bedrijfsnaam
                    : `${klant.voornaam} ${klant.achternaam}`
                  }
                </div>
                {klant.type === 'bedrijf' && klant.bedrijfsnaam && (
                  <div>t.a.v. {klant.voornaam} {klant.achternaam}</div>
                )}
                <div>{klant.adres} {klant.huisnummer}</div>
                <div>{klant.postcode} {klant.stad}</div>
              </div>
            )}
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3 text-blue-600">Factuurgegevens</h3>
            <div className="space-y-1">
              <div><span className="font-medium">Factuurdatum:</span> {new Date(factuur.factuurdatum).toLocaleDateString('nl-NL')}</div>
              <div><span className="font-medium">Vervaldatum:</span> {new Date(factuur.vervaldatum).toLocaleDateString('nl-NL')}</div>
              <div><span className="font-medium">Uitvoerdatum:</span> {new Date(factuur.uitvoerdatum).toLocaleDateString('nl-NL')}</div>
              <div><span className="font-medium">Betalingstermijn:</span> {factuur.betaaltermijn} dagen</div>
            </div>
          </div>
        </div>

        {/* Factuurregels */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-600">Werkzaamheden</h3>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-blue-50">
                <th className="border border-gray-300 px-4 py-2 text-left">Aantal</th>
                <th className="border border-gray-300 px-4 py-2 text-left">Beschrijving</th>
                <th className="border border-gray-300 px-4 py-2 text-left">Uren</th>
                <th className="border border-gray-300 px-4 py-2 text-right">Prijs/uur</th>
                <th className="border border-gray-300 px-4 py-2 text-right">Totaal</th>
              </tr>
            </thead>
            <tbody>
              {factuur.factuurregels.map((regel, index) => (
                <tr key={regel.id}>
                  <td className="border border-gray-300 px-4 py-2">{regel.aantal}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="font-medium">{regel.beschrijving}</div>
                    {regel.detailBeschrijving && (
                      <div className="text-sm text-gray-600 mt-1">{regel.detailBeschrijving}</div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">{regel.uren}</td>
                  <td className="border border-gray-300 px-4 py-2 text-right">€{regel.prijsPerUur.toFixed(2)}</td>
                  <td className="border border-gray-300 px-4 py-2 text-right">€{regel.totaalExclBtw.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Extra Kosten */}
        {(factuur.extraKosten.voorrijkosten > 0 ||
          factuur.extraKosten.spoedservice > 0 ||
          factuur.extraKosten.parkeerkosten > 0 ||
          factuur.extraKosten.materiaalkosten > 0) && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4 text-blue-600">Extra Kosten</h3>
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-blue-50">
                  <th className="border border-gray-300 px-4 py-2 text-left">Beschrijving</th>
                  <th className="border border-gray-300 px-4 py-2 text-right">Bedrag</th>
                </tr>
              </thead>
              <tbody>
                {factuur.extraKosten.voorrijkosten > 0 && (
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">Voorrijkosten</td>
                    <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.extraKosten.voorrijkosten.toFixed(2)}</td>
                  </tr>
                )}
                {factuur.extraKosten.spoedservice > 0 && (
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">Spoedservice</td>
                    <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.extraKosten.spoedservice.toFixed(2)}</td>
                  </tr>
                )}
                {factuur.extraKosten.parkeerkosten > 0 && (
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">Parkeerkosten</td>
                    <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.extraKosten.parkeerkosten.toFixed(2)}</td>
                  </tr>
                )}
                {factuur.extraKosten.materiaalkosten > 0 && (
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">
                      <div>Materiaalkosten</div>
                      {factuur.extraKosten.materiaalDetails && (
                        <div className="text-sm text-gray-600 mt-1">{factuur.extraKosten.materiaalDetails}</div>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.extraKosten.materiaalkosten.toFixed(2)}</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Totalen */}
        <div className="flex justify-end mb-8">
          <div className="w-80">
            <table className="w-full border-collapse border border-gray-300">
              <tbody>
                <tr>
                  <td className="border border-gray-300 px-4 py-2 font-medium">Subtotaal:</td>
                  <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.subtotaal.toFixed(2)}</td>
                </tr>
                {factuur.kortingBedrag > 0 && (
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium text-green-600">
                      Korting ({factuur.kortingPercentage}%):
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-right text-green-600">
                      -€{factuur.kortingBedrag.toFixed(2)}
                    </td>
                  </tr>
                )}
                <tr>
                  <td className="border border-gray-300 px-4 py-2 font-medium">BTW (21%):</td>
                  <td className="border border-gray-300 px-4 py-2 text-right">€{factuur.btw21.toFixed(2)}</td>
                </tr>
                <tr className="bg-blue-50">
                  <td className="border border-gray-300 px-4 py-2 font-bold text-lg">TOTAAL INCL. BTW:</td>
                  <td className="border border-gray-300 px-4 py-2 text-right font-bold text-lg">
                    €{factuur.totaalInclBtw.toFixed(2)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Betalingsinformatie */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-blue-600">Betalingsinformatie</h3>
            <div className="space-y-1 text-sm">
              {bedrijfsInstellingen.ibanNummer && (
                <div><span className="font-medium">IBAN:</span> {bedrijfsInstellingen.ibanNummer}</div>
              )}
              {bedrijfsInstellingen.btwNummer && (
                <div><span className="font-medium">BTW-nummer:</span> {bedrijfsInstellingen.btwNummer}</div>
              )}
              {bedrijfsInstellingen.kvkNummer && (
                <div><span className="font-medium">KvK-nummer:</span> {bedrijfsInstellingen.kvkNummer}</div>
              )}
              <div className="mt-3">
                <div className="font-medium">{bedrijfsInstellingen.betaalTekst}</div>
              </div>
            </div>
          </div>

          {factuur.betaalLink && (
            <div>
              <h3 className="text-lg font-semibold mb-3 text-blue-600">Online Betalen</h3>
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  Scan de QR-code of gebruik de link om online te betalen
                </div>
                {/* QR Code would be generated here */}
                <div className="w-24 h-24 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500">
                  QR Code
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-300 pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold mb-2">Garantie</h4>
              <div className="text-sm">
                {bedrijfsInstellingen.garantieTekst}
                {bedrijfsInstellingen.garantieDagen > 0 && (
                  <div className="mt-1">
                    Garantieperiode: {bedrijfsInstellingen.garantieDagen} dagen vanaf uitvoerdatum.
                  </div>
                )}
              </div>
            </div>

            <div>
              <div className="text-sm">
                {bedrijfsInstellingen.bedankTekst}
              </div>
            </div>
          </div>

          {bedrijfsInstellingen.algemeneVoorwaarden && (
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Algemene Voorwaarden</h4>
              <div className="text-xs text-gray-600">
                {bedrijfsInstellingen.algemeneVoorwaarden}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Status Actions */}
      {factuur.status === 'concept' && (
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-foreground mb-4">Factuur Acties</h3>
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
              <Send className="h-4 w-4" />
              <span>Factuur Verzenden</span>
            </button>
            <button className="flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
              <Euro className="h-4 w-4" />
              <span>Markeer als Betaald</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
