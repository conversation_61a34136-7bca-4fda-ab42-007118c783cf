'use client';

import React from 'react';
import { useApp } from '@/contexts/AppContext';
import { 
  ListTodo, 
  Play, 
  Trash2, 
  Clock, 
  User, 
  Building,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

export default function WachtlijstPage() {
  const { 
    wachtlijst, 
    klanten, 
    getKlant,
    removeFromWachtlijst,
    resumeFromWachtlijst
  } = useApp();

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleResumeFromWachtlijst = (wachtlijstId: string) => {
    resumeFromWachtlijst(wachtlijstId);
    // Redirect to tijdregistratie page
    window.location.href = '/tijdregistratie';
  };

  const handleRemoveFromWachtlijst = (wachtlijstId: string, klantNaam: string) => {
    if (confirm(`Weet je zeker dat je ${klantNaam} van de wachtlijst wilt verwijderen?`)) {
      removeFromWachtlijst(wachtlijstId);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Wachtlijst</h1>
          <p className="text-muted-foreground mt-1">
            Beheer klanten die tijdelijk op de wachtlijst staan
          </p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <div className="text-sm text-muted-foreground">
            {wachtlijst.length} {wachtlijst.length === 1 ? 'item' : 'items'} op wachtlijst
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totaal Wachtlijst</p>
              <p className="text-2xl font-bold text-foreground">{wachtlijst.length}</p>
            </div>
            <ListTodo className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totale Opgeslagen Tijd</p>
              <p className="text-2xl font-bold text-foreground">
                {formatTime(wachtlijst.reduce((total, item) => total + item.opgeslagenTijd, 0))}
              </p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Gemiddelde Tijd</p>
              <p className="text-2xl font-bold text-foreground">
                {wachtlijst.length > 0 
                  ? formatTime(Math.floor(wachtlijst.reduce((total, item) => total + item.opgeslagenTijd, 0) / wachtlijst.length))
                  : '00:00:00'
                }
              </p>
            </div>
            <Clock className="h-8 w-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Wachtlijst Items */}
      <div className="bg-card border border-border rounded-lg shadow-sm">
        {wachtlijst.length > 0 ? (
          <div className="divide-y divide-border">
            {wachtlijst
              .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
              .map((item) => {
                const klant = getKlant(item.klantId);
                
                return (
                  <div key={item.id} className="p-6 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className={`p-2 rounded-full ${
                            klant?.type === 'bedrijf' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                          }`}>
                            {klant?.type === 'bedrijf' ? (
                              <Building className="h-4 w-4" />
                            ) : (
                              <User className="h-4 w-4" />
                            )}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-foreground">
                              {klant ? (
                                klant.type === 'bedrijf' && klant.bedrijfsnaam 
                                  ? klant.bedrijfsnaam 
                                  : `${klant.voornaam} ${klant.achternaam}`
                              ) : 'Onbekende klant'}
                            </h3>
                            {klant?.type === 'bedrijf' && klant.bedrijfsnaam && (
                              <p className="text-sm text-muted-foreground">
                                Contactpersoon: {klant.voornaam} {klant.achternaam}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-foreground">Opgeslagen tijd:</span>
                            <div className="text-2xl font-mono font-bold text-orange-600 mt-1">
                              {formatTime(item.opgeslagenTijd)}
                            </div>
                          </div>
                          
                          <div>
                            <span className="font-medium text-foreground">Toegevoegd op:</span>
                            <div className="text-muted-foreground mt-1">
                              {new Date(item.createdAt).toLocaleDateString('nl-NL', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric'
                              })} om {new Date(item.createdAt).toLocaleTimeString('nl-NL', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>

                          {item.beschrijving && (
                            <div>
                              <span className="font-medium text-foreground">Beschrijving:</span>
                              <div className="text-muted-foreground mt-1">
                                {item.beschrijving}
                              </div>
                            </div>
                          )}
                        </div>

                        {klant && (
                          <div className="mt-3 text-sm text-muted-foreground">
                            <span>{klant.adres} {klant.huisnummer}, {klant.postcode} {klant.stad}</span>
                            <span className="mx-2">•</span>
                            <span>{klant.telefoonnummer}</span>
                            <span className="mx-2">•</span>
                            <span>{klant.email}</span>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleResumeFromWachtlijst(item.id)}
                          className="flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                          title="Hervatten"
                        >
                          <Play className="h-4 w-4" />
                          <span className="hidden sm:inline">Hervatten</span>
                        </button>
                        
                        <button
                          onClick={() => handleRemoveFromWachtlijst(
                            item.id, 
                            klant ? `${klant.voornaam} ${klant.achternaam}` : 'Onbekende klant'
                          )}
                          className="flex items-center space-x-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors"
                          title="Verwijderen"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="hidden sm:inline">Verwijderen</span>
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        ) : (
          <div className="text-center py-12">
            <ListTodo className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Wachtlijst is leeg
            </h3>
            <p className="text-muted-foreground mb-6">
              Er staan momenteel geen klanten op de wachtlijst. Klanten worden automatisch toegevoegd 
              wanneer je een tijdregistratie pauzeert en op "Wachtlijst" klikt.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/tijdregistratie"
                className="inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Clock className="h-4 w-4" />
                <span>Ga naar Tijdregistratie</span>
              </Link>
              <Link
                href="/klanten"
                className="inline-flex items-center space-x-2 bg-secondary text-secondary-foreground px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors"
              >
                <User className="h-4 w-4" />
                <span>Beheer Klanten</span>
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <div className="flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Hoe werkt de wachtlijst?</h3>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                • De wachtlijst is handig wanneer je tijdelijk moet stoppen met werken aan een klus, 
                maar de tijd wilt bewaren voor later.
              </p>
              <p>
                • Klik op "Wachtlijst" bij een actieve tijdregistratie om deze op te slaan.
              </p>
              <p>
                • Gebruik "Hervatten" om de tijdregistratie weer te activeren met de opgeslagen tijd.
              </p>
              <p>
                • De opgeslagen tijd wordt automatisch toegevoegd aan de nieuwe tijdregistratie.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      {wachtlijst.length > 0 && (
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-foreground mb-4">Snelle Acties</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <button
              onClick={() => {
                if (confirm('Weet je zeker dat je alle items van de wachtlijst wilt verwijderen?')) {
                  wachtlijst.forEach(item => removeFromWachtlijst(item.id));
                }
              }}
              className="flex items-center justify-center p-4 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Wachtlijst Legen
            </button>
            <Link
              href="/tijdregistratie"
              className="flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <Clock className="h-5 w-5 mr-2" />
              Nieuwe Tijdregistratie
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
