'use client';

import React, { useState, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import {
  Clock,
  Play,
  Pause,
  Square,
  RotateCcw,
  Plus,
  Users,
  Calculator,
  ListTodo,
  Timer
} from 'lucide-react';
import Link from 'next/link';

export default function TijdregistratiePage() {
  const {
    klanten,
    tijdregistraties,
    getKlant,
    startTijdregistratie,
    stopTijdregistratie,
    pauseTijdregistratie,
    resumeTijdregistratie,
    resetTijdregistratie,
    addTijdToRegistratie,
    addToWachtlijst
  } = useApp();

  const [selectedKlantId, setSelectedKlantId] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [extraUren, setExtraUren] = useState(0);
  const [extraMinuten, setExtraMinuten] = useState(0);

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Get active tijdregistraties
  const activeTijdregistraties = tijdregistraties.filter(t => t.status === 'actief' || t.status === 'gepauzeerd');

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getCurrentTime = (tijdregistratie: any) => {
    if (tijdregistratie.status === 'actief') {
      const elapsed = Math.floor((currentTime.getTime() - new Date(tijdregistratie.startTijd).getTime()) / 1000);
      return elapsed + tijdregistratie.totaalTijd;
    }
    return tijdregistratie.totaalTijd;
  };

  const handleStartTijdregistratie = () => {
    if (!selectedKlantId) {
      alert('Selecteer eerst een klant');
      return;
    }
    startTijdregistratie(selectedKlantId);
    setSelectedKlantId('');
  };

  const handleAddExtraTijd = (tijdregistratieId: string) => {
    if (extraUren > 0 || extraMinuten > 0) {
      addTijdToRegistratie(tijdregistratieId, extraUren, extraMinuten);
      setExtraUren(0);
      setExtraMinuten(0);
    }
  };

  const handleAddToWachtlijst = (tijdregistratie: any) => {
    const klant = getKlant(tijdregistratie.klantId);
    if (klant) {
      addToWachtlijst(tijdregistratie.klantId, tijdregistratie.id, `Wachtlijst voor ${klant.voornaam} ${klant.achternaam}`);
      pauseTijdregistratie(tijdregistratie.id);
    }
  };

  const handleCreateFactuur = (tijdregistratie: any) => {
    // Stop the tijdregistratie first if it's still active
    if (tijdregistratie.status === 'actief') {
      stopTijdregistratie(tijdregistratie.id);
    }

    // Navigate to factuur creation with tijdregistratie data
    const params = new URLSearchParams({
      klantId: tijdregistratie.klantId,
      tijdregistratieId: tijdregistratie.id,
      uren: (getCurrentTime(tijdregistratie) / 3600).toFixed(2)
    });
    window.location.href = `/facturen/nieuw?${params.toString()}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Tijdregistratie</h1>
          <p className="text-muted-foreground mt-1">
            Registreer uw werktijd per klant
          </p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <div className="text-sm text-muted-foreground">
            {currentTime.toLocaleTimeString('nl-NL')}
          </div>
        </div>
      </div>

      {/* Start New Registration */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-foreground mb-4">Nieuwe Tijdregistratie Starten</h2>
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <select
              value={selectedKlantId}
              onChange={(e) => setSelectedKlantId(e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="">Selecteer een klant...</option>
              {klanten.map((klant) => (
                <option key={klant.id} value={klant.id}>
                  {klant.type === 'bedrijf' && klant.bedrijfsnaam
                    ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`
                    : `${klant.voornaam} ${klant.achternaam}`
                  }
                </option>
              ))}
            </select>
          </div>
          <button
            onClick={handleStartTijdregistratie}
            disabled={!selectedKlantId}
            className="flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="h-4 w-4" />
            <span>Start Tijdregistratie</span>
          </button>
        </div>

        {klanten.length === 0 && (
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <p className="text-muted-foreground text-sm">
              Geen klanten beschikbaar.
              <Link href="/klanten/nieuw" className="text-primary hover:underline ml-1">
                Voeg eerst een klant toe
              </Link>
            </p>
          </div>
        )}
      </div>

      {/* Active Registrations */}
      {activeTijdregistraties.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-foreground">Actieve Tijdregistraties</h2>

          {activeTijdregistraties.map((tijdregistratie) => {
            const klant = getKlant(tijdregistratie.klantId);
            const currentSeconds = getCurrentTime(tijdregistratie);

            return (
              <div key={tijdregistratie.id} className="bg-card border border-border rounded-lg p-6 shadow-sm">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  {/* Klant Info */}
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-foreground mb-2">
                      {klant ? (
                        klant.type === 'bedrijf' && klant.bedrijfsnaam
                          ? `${klant.bedrijfsnaam}`
                          : `${klant.voornaam} ${klant.achternaam}`
                      ) : 'Onbekende klant'}
                    </h3>
                    {klant && (
                      <div className="text-sm text-muted-foreground space-y-1">
                        {klant.type === 'bedrijf' && klant.bedrijfsnaam && (
                          <p>Contactpersoon: {klant.voornaam} {klant.achternaam}</p>
                        )}
                        <p>{klant.adres} {klant.huisnummer}, {klant.postcode} {klant.stad}</p>
                        <p>{klant.telefoonnummer} • {klant.email}</p>
                      </div>
                    )}
                  </div>

                  {/* Stopwatch */}
                  <div className="text-center">
                    <div className="text-4xl font-mono font-bold text-foreground mb-2">
                      {formatTime(currentSeconds)}
                    </div>
                    <div className={`text-sm font-medium ${
                      tijdregistratie.status === 'actief' ? 'text-green-600' : 'text-orange-600'
                    }`}>
                      {tijdregistratie.status === 'actief' ? 'Actief' : 'Gepauzeerd'}
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="flex flex-col space-y-2 lg:ml-6">
                    <div className="flex items-center space-x-2">
                      {tijdregistratie.status === 'actief' ? (
                        <button
                          onClick={() => pauseTijdregistratie(tijdregistratie.id)}
                          className="flex items-center space-x-1 bg-orange-500 text-white px-3 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                        >
                          <Pause className="h-4 w-4" />
                          <span>Pauze</span>
                        </button>
                      ) : (
                        <button
                          onClick={() => resumeTijdregistratie(tijdregistratie.id)}
                          className="flex items-center space-x-1 bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors"
                        >
                          <Play className="h-4 w-4" />
                          <span>Hervatten</span>
                        </button>
                      )}

                      <button
                        onClick={() => stopTijdregistratie(tijdregistratie.id)}
                        className="flex items-center space-x-1 bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors"
                      >
                        <Square className="h-4 w-4" />
                        <span>Stop</span>
                      </button>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => resetTijdregistratie(tijdregistratie.id)}
                        className="flex items-center space-x-1 bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                      >
                        <RotateCcw className="h-4 w-4" />
                        <span>Reset</span>
                      </button>

                      <button
                        onClick={() => handleAddToWachtlijst(tijdregistratie)}
                        className="flex items-center space-x-1 bg-purple-500 text-white px-3 py-2 rounded-lg hover:bg-purple-600 transition-colors"
                      >
                        <ListTodo className="h-4 w-4" />
                        <span>Wachtlijst</span>
                      </button>
                    </div>

                    {/* Extra tijd toevoegen */}
                    <div className="flex items-center space-x-2 pt-2 border-t border-border">
                      <div className="flex items-center space-x-1">
                        <input
                          type="number"
                          min="0"
                          max="23"
                          value={extraUren}
                          onChange={(e) => setExtraUren(parseInt(e.target.value) || 0)}
                          className="w-16 px-2 py-1 text-sm border border-input rounded bg-background text-foreground"
                          placeholder="0"
                        />
                        <span className="text-xs text-muted-foreground">u</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <input
                          type="number"
                          min="0"
                          max="59"
                          value={extraMinuten}
                          onChange={(e) => setExtraMinuten(parseInt(e.target.value) || 0)}
                          className="w-16 px-2 py-1 text-sm border border-input rounded bg-background text-foreground"
                          placeholder="0"
                        />
                        <span className="text-xs text-muted-foreground">m</span>
                      </div>
                      <button
                        onClick={() => handleAddExtraTijd(tijdregistratie.id)}
                        className="flex items-center space-x-1 bg-blue-500 text-white px-2 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                      >
                        <Plus className="h-3 w-3" />
                        <span>Tijd</span>
                      </button>
                    </div>

                    {/* Factuur maken */}
                    <div className="pt-2 border-t border-border">
                      <button
                        onClick={() => handleCreateFactuur(tijdregistratie)}
                        className="flex items-center space-x-1 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors w-full justify-center"
                      >
                        <Calculator className="h-4 w-4" />
                        <span>Factuur Maken</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* No Active Registrations */}
      {activeTijdregistraties.length === 0 && (
        <div className="bg-card border border-border rounded-lg p-12 text-center shadow-sm">
          <Timer className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-foreground mb-2">
            Geen actieve tijdregistraties
          </h3>
          <p className="text-muted-foreground mb-6">
            Start een nieuwe tijdregistratie door een klant te selecteren en op "Start Tijdregistratie" te klikken.
          </p>
          {klanten.length === 0 && (
            <Link
              href="/klanten/nieuw"
              className="inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Users className="h-4 w-4" />
              <span>Eerste Klant Toevoegen</span>
            </Link>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-foreground mb-4">Snelle Acties</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Link
            href="/wachtlijst"
            className="flex items-center justify-center p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            <ListTodo className="h-5 w-5 mr-2" />
            Bekijk Wachtlijst
          </Link>
          <Link
            href="/snelle-berekening"
            className="flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <Calculator className="h-5 w-5 mr-2" />
            Snelle Berekening
          </Link>
          <Link
            href="/klanten"
            className="flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Users className="h-5 w-5 mr-2" />
            Klanten Beheren
          </Link>
        </div>
      </div>
    </div>
  );
}
