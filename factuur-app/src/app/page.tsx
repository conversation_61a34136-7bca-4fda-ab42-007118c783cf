'use client';

import React from 'react';
import { useApp } from '@/contexts/AppContext';
import {
  Users,
  Clock,
  FileText,
  TrendingUp,
  Calendar,
  Euro,
  Activity,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const { klanten, tijdregistraties, facturen, wachtlijst } = useApp();

  // Calculate statistics
  const stats = {
    totalKlanten: klanten.length,
    activeTijdregistraties: tijdregistraties.filter(t => t.status === 'actief').length,
    conceptFacturen: facturen.filter(f => f.status === 'concept').length,
    wachtlijstItems: wachtlijst.length,
    totalFacturen: facturen.length,
    totalOmzet: facturen
      .filter(f => f.status === 'betaald')
      .reduce((sum, f) => sum + f.totaalInclBtw, 0),
    openstaandeFacturen: facturen
      .filter(f => f.status === 'verzonden')
      .reduce((sum, f) => sum + f.totaalInclBtw, 0),
  };

  const recenteActiviteiten = [
    ...tijdregistraties
      .filter(t => t.status === 'actief')
      .slice(0, 3)
      .map(t => ({
        type: 'tijdregistratie',
        klant: klanten.find(k => k.id === t.klantId),
        tijd: t.createdAt,
        beschrijving: 'Tijdregistratie actief'
      })),
    ...facturen
      .filter(f => f.status === 'concept')
      .slice(0, 2)
      .map(f => ({
        type: 'factuur',
        klant: klanten.find(k => k.id === f.klantId),
        tijd: f.createdAt,
        beschrijving: 'Concept factuur'
      }))
  ].sort((a, b) => b.tijd.getTime() - a.tijd.getTime()).slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Overzicht van uw factuur activiteiten
          </p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <div className="text-sm text-muted-foreground">
            {new Date().toLocaleDateString('nl-NL', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totaal Klanten</p>
              <p className="text-2xl font-bold text-foreground">{stats.totalKlanten}</p>
            </div>
            <Users className="h-8 w-8 text-primary" />
          </div>
          <div className="mt-4">
            <Link
              href="/klanten"
              className="text-sm text-primary hover:underline"
            >
              Bekijk alle klanten →
            </Link>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Actieve Tijdregistraties</p>
              <p className="text-2xl font-bold text-foreground">{stats.activeTijdregistraties}</p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
          <div className="mt-4">
            <Link
              href="/tijdregistratie"
              className="text-sm text-primary hover:underline"
            >
              Ga naar tijdregistratie →
            </Link>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Concept Facturen</p>
              <p className="text-2xl font-bold text-foreground">{stats.conceptFacturen}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-4">
            <Link
              href="/facturen"
              className="text-sm text-primary hover:underline"
            >
              Bekijk facturen →
            </Link>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Wachtlijst</p>
              <p className="text-2xl font-bold text-foreground">{stats.wachtlijstItems}</p>
            </div>
            <Activity className="h-8 w-8 text-purple-500" />
          </div>
          <div className="mt-4">
            <Link
              href="/wachtlijst"
              className="text-sm text-primary hover:underline"
            >
              Bekijk wachtlijst →
            </Link>
          </div>
        </div>
      </div>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Totale Omzet</h3>
            <TrendingUp className="h-5 w-5 text-green-500" />
          </div>
          <p className="text-3xl font-bold text-green-600">
            €{stats.totalOmzet.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Van {stats.totalFacturen} facturen
          </p>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Openstaand</h3>
            <Euro className="h-5 w-5 text-orange-500" />
          </div>
          <p className="text-3xl font-bold text-orange-600">
            €{stats.openstaandeFacturen.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Nog te ontvangen
          </p>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Deze Maand</h3>
            <Calendar className="h-5 w-5 text-blue-500" />
          </div>
          <p className="text-3xl font-bold text-blue-600">
            €{facturen
              .filter(f => {
                const now = new Date();
                const factuurDate = new Date(f.factuurdatum);
                return factuurDate.getMonth() === now.getMonth() &&
                       factuurDate.getFullYear() === now.getFullYear();
              })
              .reduce((sum, f) => sum + f.totaalInclBtw, 0)
              .toFixed(2)
            }
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Facturen deze maand
          </p>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-foreground mb-4">Recente Activiteiten</h3>
        {recenteActiviteiten.length > 0 ? (
          <div className="space-y-4">
            {recenteActiviteiten.map((activiteit, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 bg-muted rounded-lg">
                <div className={`p-2 rounded-full ${
                  activiteit.type === 'tijdregistratie' ? 'bg-orange-100 text-orange-600' : 'bg-blue-100 text-blue-600'
                }`}>
                  {activiteit.type === 'tijdregistratie' ? (
                    <Clock className="h-4 w-4" />
                  ) : (
                    <FileText className="h-4 w-4" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-foreground">
                    {activiteit.beschrijving}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {activiteit.klant ? `${activiteit.klant.voornaam} ${activiteit.klant.achternaam}` : 'Onbekende klant'} •
                    {activiteit.tijd.toLocaleDateString('nl-NL')} om {activiteit.tijd.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Geen recente activiteiten</p>
            <p className="text-sm text-muted-foreground mt-2">
              Start met het toevoegen van klanten en tijdregistraties
            </p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-foreground mb-4">Snelle Acties</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/klanten/nieuw"
            className="flex items-center justify-center p-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Users className="h-5 w-5 mr-2" />
            Nieuwe Klant
          </Link>
          <Link
            href="/tijdregistratie"
            className="flex items-center justify-center p-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <Clock className="h-5 w-5 mr-2" />
            Start Tijdregistratie
          </Link>
          <Link
            href="/snelle-berekening"
            className="flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <TrendingUp className="h-5 w-5 mr-2" />
            Snelle Berekening
          </Link>
          <Link
            href="/facturen/nieuw"
            className="flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <FileText className="h-5 w-5 mr-2" />
            Nieuwe Factuur
          </Link>
        </div>
      </div>
    </div>
  );
}
