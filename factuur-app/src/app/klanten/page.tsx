'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Clock, 
  Phone, 
  MessageSquare, 
  Navigation,
  Building,
  User,
  Mail,
  MapPin
} from 'lucide-react';
import Link from 'next/link';

export default function KlantenPage() {
  const { klanten, deleteKlant, startTijdregistratie } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'particulier' | 'bedrijf'>('all');

  // Filter klanten based on search and type
  const filteredKlanten = klanten.filter(klant => {
    const matchesSearch = 
      klant.voornaam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      klant.achternaam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (klant.bedrijfsnaam && klant.bedrijfsnaam.toLowerCase().includes(searchTerm.toLowerCase())) ||
      klant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      klant.telefoonnummer.includes(searchTerm);
    
    const matchesType = filterType === 'all' || klant.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const handleDeleteKlant = (id: string, naam: string) => {
    if (confirm(`Weet je zeker dat je ${naam} wilt verwijderen?`)) {
      deleteKlant(id);
    }
  };

  const handleStartTijdregistratie = (klantId: string) => {
    startTijdregistratie(klantId);
    // Redirect to tijdregistratie page
    window.location.href = '/tijdregistratie';
  };

  const handleContactAction = (klant: any, action: string) => {
    switch (action) {
      case 'whatsapp':
        window.open(`https://wa.me/${klant.telefoonnummer.replace(/\D/g, '')}`, '_blank');
        break;
      case 'sms':
        window.open(`sms:${klant.telefoonnummer}`, '_blank');
        break;
      case 'call':
        window.open(`tel:${klant.telefoonnummer}`, '_blank');
        break;
      case 'route':
        const address = `${klant.adres} ${klant.huisnummer}, ${klant.postcode} ${klant.stad}`;
        window.open(`https://maps.google.com?q=${encodeURIComponent(address)}`, '_blank');
        break;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Klanten</h1>
          <p className="text-muted-foreground mt-1">
            Beheer uw klanten en contactgegevens
          </p>
        </div>
        <Link
          href="/klanten/nieuw"
          className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors mt-4 sm:mt-0"
        >
          <Plus className="h-4 w-4" />
          <span>Nieuwe Klant</span>
        </Link>
      </div>

      {/* Search and Filter */}
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Zoek klanten..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="px-4 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="all">Alle klanten</option>
            <option value="particulier">Particulieren</option>
            <option value="bedrijf">Bedrijven</option>
          </select>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Totaal Klanten</p>
              <p className="text-2xl font-bold text-foreground">{klanten.length}</p>
            </div>
            <Users className="h-8 w-8 text-primary" />
          </div>
        </div>
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Particulieren</p>
              <p className="text-2xl font-bold text-foreground">
                {klanten.filter(k => k.type === 'particulier').length}
              </p>
            </div>
            <User className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Bedrijven</p>
              <p className="text-2xl font-bold text-foreground">
                {klanten.filter(k => k.type === 'bedrijf').length}
              </p>
            </div>
            <Building className="h-8 w-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Klanten List */}
      <div className="bg-card border border-border rounded-lg shadow-sm">
        {filteredKlanten.length > 0 ? (
          <div className="divide-y divide-border">
            {filteredKlanten.map((klant) => (
              <div key={klant.id} className="p-6 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-2 rounded-full ${
                        klant.type === 'bedrijf' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                      }`}>
                        {klant.type === 'bedrijf' ? (
                          <Building className="h-4 w-4" />
                        ) : (
                          <User className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-foreground">
                          {klant.type === 'bedrijf' && klant.bedrijfsnaam 
                            ? klant.bedrijfsnaam 
                            : `${klant.voornaam} ${klant.achternaam}`
                          }
                        </h3>
                        {klant.type === 'bedrijf' && klant.bedrijfsnaam && (
                          <p className="text-sm text-muted-foreground">
                            {klant.voornaam} {klant.achternaam}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{klant.adres} {klant.huisnummer}, {klant.postcode} {klant.stad}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>{klant.telefoonnummer}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span>{klant.email}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                    {/* Contact Actions */}
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleContactAction(klant, 'whatsapp')}
                        className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                        title="WhatsApp"
                      >
                        <MessageSquare className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleContactAction(klant, 'call')}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        title="Bellen"
                      >
                        <Phone className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleContactAction(klant, 'route')}
                        className="p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors"
                        title="Route"
                      >
                        <Navigation className="h-4 w-4" />
                      </button>
                    </div>
                    
                    {/* Main Actions */}
                    <div className="flex items-center space-x-1 border-l border-border pl-2">
                      <button
                        onClick={() => handleStartTijdregistratie(klant.id)}
                        className="p-2 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors"
                        title="Start Tijdregistratie"
                      >
                        <Clock className="h-4 w-4" />
                      </button>
                      <Link
                        href={`/klanten/${klant.id}/bewerken`}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        title="Bewerken"
                      >
                        <Edit className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => handleDeleteKlant(klant.id, `${klant.voornaam} ${klant.achternaam}`)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        title="Verwijderen"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {searchTerm || filterType !== 'all' ? 'Geen klanten gevonden' : 'Nog geen klanten'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm || filterType !== 'all' 
                ? 'Probeer een andere zoekopdracht of filter.'
                : 'Voeg uw eerste klant toe om te beginnen.'
              }
            </p>
            {!searchTerm && filterType === 'all' && (
              <Link
                href="/klanten/nieuw"
                className="inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>Eerste Klant Toevoegen</span>
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
