'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import {
  Calculator,
  User,
  Building,
  Euro,
  FileText,
  Eye,
  Share2,
  Download
} from 'lucide-react';

export default function SnelleBerekeningPage() {
  const { klanten, bedrijfsInstellingen } = useApp();

  const [selectedKlantId, setSelectedKlantId] = useState('');
  const [formData, setFormData] = useState({
    serviceType: 'loodgieter' as const,
    customService: '',
    klusbeschrijving: '',
    uurtarief: bedrijfsInstellingen.standaardUurtarief,
    uren: 0,
    minuten: 0,
    btw21Percentage: bedrijfsInstellingen.standaardBtw21,
    btw9Percentage: bedrijfsInstellingen.standaardBtw9,
    voorrijkosten: bedrijfsInstellingen.standaardVoorrijkosten,
    spoedservice: bedrijfsInstellingen.standaardSpoedservice,
    parkeerkosten: 0,
    materiaalkosten: 0,
    materiaalDetails: '',
    kortingPercentage: 0,
  });

  const [berekening, setBerekening] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);

  const serviceTypes = [
    { value: 'loodgieter', label: 'Loodgieter' },
    { value: 'elektricien', label: 'Elektricien' },
    { value: 'klusjesman', label: 'Klusjesman' },
    { value: 'timmerman', label: 'Timmerman' },
    { value: 'aannemer', label: 'Aannemer' },
    { value: 'custom', label: 'Anders...' },
  ];

  const selectedKlant = klanten.find(k => k.id === selectedKlantId);
  const totalUren = formData.uren + (formData.minuten / 60);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const berekenTotaal = () => {
    const arbeidskosten = totalUren * formData.uurtarief;
    const extraKosten = formData.voorrijkosten + formData.spoedservice + formData.parkeerkosten + formData.materiaalkosten;
    const subtotaal = arbeidskosten + extraKosten;

    const kortingBedrag = (subtotaal * formData.kortingPercentage) / 100;
    const subtotaalNaKorting = subtotaal - kortingBedrag;

    // Voor eenvoud: alle kosten onder 21% BTW
    const btw21 = (subtotaalNaKorting * formData.btw21Percentage) / 100;
    const btw9 = 0; // Voor deze berekening gebruiken we alleen 21% BTW

    const totaalInclBtw = subtotaalNaKorting + btw21 + btw9;

    const result = {
      arbeidskosten,
      extraKosten,
      subtotaal,
      kortingBedrag,
      subtotaalNaKorting,
      btw21,
      btw9,
      totaalInclBtw,
      klant: selectedKlant,
      formData: { ...formData, totalUren }
    };

    setBerekening(result);
    return result;
  };

  const handleBerekenen = () => {
    if (!selectedKlantId) {
      alert('Selecteer eerst een klant');
      return;
    }
    if (totalUren === 0) {
      alert('Voer een geldige tijd in');
      return;
    }
    berekenTotaal();
  };

  const handlePreview = () => {
    if (berekening) {
      setShowPreview(true);
    }
  };

  const handleGeneratePDF = async () => {
    if (!berekening || !selectedKlant) return;

    try {
      // Import the professional PDF generator
      const { generateBerekeningPDF } = await import('@/utils/berekeningPdfGenerator');

      // Prepare berekening data
      const berekeningData = {
        klant: selectedKlant,
        serviceType: formData.serviceType,
        customService: formData.customService,
        klusbeschrijving: formData.klusbeschrijving,
        totalUren: totalUren,
        uurtarief: formData.uurtarief,
        arbeidskosten: berekening.arbeidskosten,
        extraKosten: berekening.extraKosten,
        subtotaal: berekening.subtotaal,
        kortingBedrag: berekening.kortingBedrag,
        kortingPercentage: formData.kortingPercentage,
        btw21: berekening.btw21,
        totaalInclBtw: berekening.totaalInclBtw,
        bedrijfsInstellingen: bedrijfsInstellingen,
        formData: formData
      };

      // Generate PDF
      const pdf = await generateBerekeningPDF(berekeningData);

      // Save PDF
      const fileName = `Kostenberekening-${selectedKlant.voornaam}-${selectedKlant.achternaam}-${new Date().toLocaleDateString('nl-NL')}.pdf`;
      pdf.save(fileName);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Er is een fout opgetreden bij het genereren van de PDF');
    }
  };

  const handleShare = (method: 'whatsapp' | 'sms' | 'email') => {
    if (!berekening || !selectedKlant) return;

    const message = `
Kostenberekening voor ${selectedKlant.voornaam} ${selectedKlant.achternaam}

Service: ${formData.serviceType === 'custom' ? formData.customService : serviceTypes.find(s => s.value === formData.serviceType)?.label}
Beschrijving: ${formData.klusbeschrijving}

Arbeidskosten: ${totalUren.toFixed(2)} uur × €${formData.uurtarief} = €${berekening.arbeidskosten.toFixed(2)}
Extra kosten: €${berekening.extraKosten.toFixed(2)}
Subtotaal: €${berekening.subtotaal.toFixed(2)}
${berekening.kortingBedrag > 0 ? `Korting (${formData.kortingPercentage}%): -€${berekening.kortingBedrag.toFixed(2)}\n` : ''}
BTW (21%): €${berekening.btw21.toFixed(2)}

TOTAAL: €${berekening.totaalInclBtw.toFixed(2)}
    `.trim();

    switch (method) {
      case 'whatsapp':
        window.open(`https://wa.me/${selectedKlant.telefoonnummer.replace(/\D/g, '')}?text=${encodeURIComponent(message)}`, '_blank');
        break;
      case 'sms':
        window.open(`sms:${selectedKlant.telefoonnummer}?body=${encodeURIComponent(message)}`, '_blank');
        break;
      case 'email':
        window.open(`mailto:${selectedKlant.email}?subject=${encodeURIComponent('Kostenberekening')}&body=${encodeURIComponent(message)}`, '_blank');
        break;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Snelle Berekening</h1>
          <p className="text-muted-foreground mt-1">
            Bereken snel de kosten voor een klus
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Formulier */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-foreground mb-4">Klus Details</h2>

          <div className="space-y-4">
            {/* Klant Selectie */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Klant *
              </label>
              <select
                value={selectedKlantId}
                onChange={(e) => setSelectedKlantId(e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="">Selecteer een klant...</option>
                {klanten.map((klant) => (
                  <option key={klant.id} value={klant.id}>
                    {klant.type === 'bedrijf' && klant.bedrijfsnaam
                      ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`
                      : `${klant.voornaam} ${klant.achternaam}`
                    }
                  </option>
                ))}
              </select>
            </div>

            {/* Service Type */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Service Type
              </label>
              <select
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                {serviceTypes.map((service) => (
                  <option key={service.value} value={service.value}>
                    {service.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Custom Service */}
            {formData.serviceType === 'custom' && (
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Aangepaste Service
                </label>
                <input
                  type="text"
                  name="customService"
                  value={formData.customService}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="Beschrijf de service..."
                />
              </div>
            )}

            {/* Klusbeschrijving */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Klusbeschrijving
              </label>
              <textarea
                name="klusbeschrijving"
                value={formData.klusbeschrijving}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                placeholder="Beschrijf de uitgevoerde werkzaamheden..."
              />
            </div>

            {/* Tijd en Tarief */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Uren
                </label>
                <input
                  type="number"
                  name="uren"
                  value={formData.uren}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Minuten
                </label>
                <input
                  type="number"
                  name="minuten"
                  value={formData.minuten}
                  onChange={handleInputChange}
                  min="0"
                  max="59"
                  className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Uurtarief (€)
              </label>
              <input
                type="number"
                name="uurtarief"
                value={formData.uurtarief}
                onChange={handleInputChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>

            {/* Extra Kosten */}
            <div className="space-y-3">
              <h3 className="text-md font-semibold text-foreground">Extra Kosten</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Voorrijkosten (€)
                  </label>
                  <input
                    type="number"
                    name="voorrijkosten"
                    value={formData.voorrijkosten}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Spoedservice (€)
                  </label>
                  <input
                    type="number"
                    name="spoedservice"
                    value={formData.spoedservice}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Parkeerkosten (€)
                  </label>
                  <input
                    type="number"
                    name="parkeerkosten"
                    value={formData.parkeerkosten}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Materiaalkosten (€)
                  </label>
                  <input
                    type="number"
                    name="materiaalkosten"
                    value={formData.materiaalkosten}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>
              </div>

              {formData.materiaalkosten > 0 && (
                <div>
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Materiaal Details
                  </label>
                  <input
                    type="text"
                    name="materiaalDetails"
                    value={formData.materiaalDetails}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Beschrijf het gebruikte materiaal..."
                  />
                </div>
              )}
            </div>

            {/* Korting */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Korting (%)
              </label>
              <select
                name="kortingPercentage"
                value={formData.kortingPercentage}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value={0}>Geen korting</option>
                <option value={5}>5%</option>
                <option value={10}>10%</option>
                <option value={15}>15%</option>
                <option value={20}>20%</option>
                <option value={25}>25%</option>
              </select>
            </div>

            {/* Berekenen Button */}
            <button
              onClick={handleBerekenen}
              className="w-full flex items-center justify-center space-x-2 bg-primary text-primary-foreground px-4 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Calculator className="h-5 w-5" />
              <span>Berekenen</span>
            </button>
          </div>
        </div>

        {/* Resultaat */}
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-foreground mb-4">Berekening</h2>

          {berekening ? (
            <div className="space-y-4">
              {/* Klant Info */}
              {selectedKlant && (
                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    {selectedKlant.type === 'bedrijf' ? (
                      <Building className="h-4 w-4 text-green-600" />
                    ) : (
                      <User className="h-4 w-4 text-blue-600" />
                    )}
                    <span className="font-medium">
                      {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam
                        ? selectedKlant.bedrijfsnaam
                        : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`
                      }
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {selectedKlant.adres} {selectedKlant.huisnummer}, {selectedKlant.postcode} {selectedKlant.stad}
                  </div>
                </div>
              )}

              {/* Berekening Details */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Arbeidskosten ({totalUren.toFixed(2)} uur × €{formData.uurtarief}):</span>
                  <span>€{berekening.arbeidskosten.toFixed(2)}</span>
                </div>

                {berekening.extraKosten > 0 && (
                  <div className="flex justify-between">
                    <span>Extra kosten:</span>
                    <span>€{berekening.extraKosten.toFixed(2)}</span>
                  </div>
                )}

                <div className="flex justify-between font-medium border-t border-border pt-2">
                  <span>Subtotaal:</span>
                  <span>€{berekening.subtotaal.toFixed(2)}</span>
                </div>

                {berekening.kortingBedrag > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Korting ({formData.kortingPercentage}%):</span>
                    <span>-€{berekening.kortingBedrag.toFixed(2)}</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span>BTW (21%):</span>
                  <span>€{berekening.btw21.toFixed(2)}</span>
                </div>

                <div className="flex justify-between text-xl font-bold border-t border-border pt-3">
                  <span>TOTAAL INCL. BTW:</span>
                  <span className="text-green-600">€{berekening.totaalInclBtw.toFixed(2)}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3 pt-4 border-t border-border">
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={handlePreview}
                    className="flex items-center justify-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    <Eye className="h-4 w-4" />
                    <span>Preview</span>
                  </button>
                  <button
                    onClick={handleGeneratePDF}
                    className="flex items-center justify-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                  >
                    <Download className="h-4 w-4" />
                    <span>PDF Opslaan</span>
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => handleShare('whatsapp')}
                    className="flex items-center justify-center space-x-1 bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm"
                  >
                    <Share2 className="h-3 w-3" />
                    <span>WhatsApp</span>
                  </button>
                  <button
                    onClick={() => handleShare('sms')}
                    className="flex items-center justify-center space-x-1 bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm"
                  >
                    <Share2 className="h-3 w-3" />
                    <span>SMS</span>
                  </button>
                  <button
                    onClick={() => handleShare('email')}
                    className="flex items-center justify-center space-x-1 bg-purple-500 text-white px-3 py-2 rounded-lg hover:bg-purple-600 transition-colors text-sm"
                  >
                    <Share2 className="h-3 w-3" />
                    <span>E-mail</span>
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Vul de gegevens in en klik op "Berekenen" om de kosten te zien
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && berekening && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">Kostenberekening Preview</h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  ✕
                </button>
              </div>

              {/* Preview Content */}
              <div id="berekening-content" className="space-y-4 text-sm bg-white text-black p-6 rounded-lg">
                <div className="text-center">
                  <h2 className="text-lg font-bold">Kostenberekening</h2>
                  <p className="text-muted-foreground">
                    {new Date().toLocaleDateString('nl-NL')}
                  </p>
                </div>

                {selectedKlant && (
                  <div>
                    <h3 className="font-semibold mb-2">Klantgegevens:</h3>
                    <p>{selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam
                        ? selectedKlant.bedrijfsnaam
                        : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`}</p>
                    <p>{selectedKlant.adres} {selectedKlant.huisnummer}</p>
                    <p>{selectedKlant.postcode} {selectedKlant.stad}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-semibold mb-2">Werkzaamheden:</h3>
                  <p><strong>Service:</strong> {formData.serviceType === 'custom' ? formData.customService : serviceTypes.find(s => s.value === formData.serviceType)?.label}</p>
                  <p><strong>Beschrijving:</strong> {formData.klusbeschrijving}</p>
                  <p><strong>Tijd:</strong> {totalUren.toFixed(2)} uur</p>
                  <p><strong>Uurtarief:</strong> €{formData.uurtarief}</p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Kostenopbouw:</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>Arbeidskosten:</span>
                      <span>€{berekening.arbeidskosten.toFixed(2)}</span>
                    </div>
                    {berekening.extraKosten > 0 && (
                      <div className="flex justify-between">
                        <span>Extra kosten:</span>
                        <span>€{berekening.extraKosten.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-medium border-t pt-1">
                      <span>Subtotaal:</span>
                      <span>€{berekening.subtotaal.toFixed(2)}</span>
                    </div>
                    {berekening.kortingBedrag > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Korting ({formData.kortingPercentage}%):</span>
                        <span>-€{berekening.kortingBedrag.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>BTW (21%):</span>
                      <span>€{berekening.btw21.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold border-t pt-2">
                      <span>TOTAAL INCL. BTW:</span>
                      <span>€{berekening.totaalInclBtw.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6 pt-4 border-t">
                <button
                  onClick={() => setShowPreview(false)}
                  className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
                >
                  Sluiten
                </button>
                <button
                  onClick={handleGeneratePDF}
                  className="flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                >
                  <Download className="h-4 w-4" />
                  <span>PDF Opslaan</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
