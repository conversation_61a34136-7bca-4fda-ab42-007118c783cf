'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { 
  Settings, 
  Save, 
  Building, 
  FileText, 
  Euro,
  Upload,
  Palette,
  Shield
} from 'lucide-react';

export default function InstellingenPage() {
  const { bedrijfsInstellingen, updateBedrijfsInstellingen } = useApp();
  
  const [formData, setFormData] = useState(bedrijfsInstellingen);
  const [activeTab, setActiveTab] = useState('bedrijf');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  const tabs = [
    { id: 'bedrijf', label: 'Bedrijfsgegevens', icon: Building },
    { id: 'factuur', label: 'Factuurinstellingen', icon: FileText },
    { id: 'tarieven', label: 'Tarieven & Kosten', icon: Euro },
    { id: 'styling', label: 'Styling', icon: Palette },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      updateBedrijfsInstellingen(formData);
      setSaveMessage('Instellingen succesvol opgeslagen!');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      setSaveMessage('Er is een fout opgetreden bij het opslaan.');
      setTimeout(() => setSaveMessage(''), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setFormData(prev => ({ ...prev, logo: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const renderBedrijfTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Bedrijfsinformatie</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Bedrijfsnaam *
            </label>
            <input
              type="text"
              name="bedrijfsnaam"
              value={formData.bedrijfsnaam}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Uw bedrijfsnaam"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              E-mailadres *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Telefoonnummer *
            </label>
            <input
              type="tel"
              name="telefoonnummer"
              value={formData.telefoonnummer}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="06-12345678"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Website
            </label>
            <input
              type="url"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="https://www.uwbedrijf.nl"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Adresgegevens</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-foreground mb-2">
              Adres *
            </label>
            <input
              type="text"
              name="adres"
              value={formData.adres}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Straatnaam + huisnummer"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Postcode *
            </label>
            <input
              type="text"
              name="postcode"
              value={formData.postcode}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="1234 AB"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Stad *
            </label>
            <input
              type="text"
              name="stad"
              value={formData.stad}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Amsterdam"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Bedrijfsregistratie</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              KvK Nummer
            </label>
            <input
              type="text"
              name="kvkNummer"
              value={formData.kvkNummer}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="12345678"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              BTW Nummer
            </label>
            <input
              type="text"
              name="btwNummer"
              value={formData.btwNummer}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="NL123456789B01"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              IBAN Nummer
            </label>
            <input
              type="text"
              name="ibanNummer"
              value={formData.ibanNummer}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="NL91 ABNA 0417 1643 00"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Bedrijfslogo</h3>
        
        <div className="flex items-center space-x-4">
          {formData.logo && (
            <div className="w-16 h-16 border border-border rounded-lg overflow-hidden">
              <img 
                src={formData.logo} 
                alt="Logo" 
                className="w-full h-full object-contain"
              />
            </div>
          )}
          
          <div>
            <label className="flex items-center space-x-2 bg-secondary text-secondary-foreground px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors cursor-pointer">
              <Upload className="h-4 w-4" />
              <span>Logo Uploaden</span>
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
              />
            </label>
            <p className="text-xs text-muted-foreground mt-1">
              Ondersteunde formaten: JPG, PNG, GIF (max 2MB)
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFactuurTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Factuurnummering</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Prefix
            </label>
            <input
              type="text"
              name="factuurnummerPrefix"
              value={formData.factuurnummerPrefix}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="F"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Volgende nummer
            </label>
            <input
              type="number"
              name="factuurnummerCounter"
              value={formData.factuurnummerCounter}
              onChange={handleInputChange}
              min="1"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>
        
        <div className="mt-2 p-3 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground">
            Voorbeeld factuurnummer: <span className="font-mono">{formData.factuurnummerPrefix}-{new Date().getFullYear()}-{formData.factuurnummerCounter.toString().padStart(4, '0')}</span>
          </p>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Standaard Betalingstermijn</h3>
        
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Betalingstermijn (dagen)
          </label>
          <select
            name="standaardBetaaltermijn"
            value={formData.standaardBetaaltermijn}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value={7}>7 dagen</option>
            <option value={14}>14 dagen</option>
            <option value={30}>30 dagen</option>
          </select>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Garantie</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Garantietekst
            </label>
            <textarea
              name="garantieTekst"
              value={formData.garantieTekst}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Op alle werkzaamheden geven wij garantie..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Garantieperiode (dagen)
            </label>
            <input
              type="number"
              name="garantieDagen"
              value={formData.garantieDagen}
              onChange={handleInputChange}
              min="0"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Factuur Teksten</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Betaaltekst
            </label>
            <textarea
              name="betaalTekst"
              value={formData.betaalTekst}
              onChange={handleInputChange}
              rows={2}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Gelieve het factuurbedrag binnen de betalingstermijn over te maken..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Bedanktekst
            </label>
            <textarea
              name="bedankTekst"
              value={formData.bedankTekst}
              onChange={handleInputChange}
              rows={2}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Bedankt voor uw vertrouwen in onze dienstverlening..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Algemene Voorwaarden
            </label>
            <textarea
              name="algemeneVoorwaarden"
              value={formData.algemeneVoorwaarden}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="Voer hier uw algemene voorwaarden in..."
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderTarievenTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Standaard Uurtarief</h3>
        
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Uurtarief (€)
          </label>
          <input
            type="number"
            name="standaardUurtarief"
            value={formData.standaardUurtarief}
            onChange={handleInputChange}
            step="0.01"
            min="0"
            className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">BTW Tarieven</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Hoog BTW tarief (%)
            </label>
            <input
              type="number"
              name="standaardBtw21"
              value={formData.standaardBtw21}
              onChange={handleInputChange}
              step="0.1"
              min="0"
              max="100"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Laag BTW tarief (%)
            </label>
            <input
              type="number"
              name="standaardBtw9"
              value={formData.standaardBtw9}
              onChange={handleInputChange}
              step="0.1"
              min="0"
              max="100"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Standaard Extra Kosten</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Voorrijkosten (€)
            </label>
            <input
              type="number"
              name="standaardVoorrijkosten"
              value={formData.standaardVoorrijkosten}
              onChange={handleInputChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Spoedservice (€)
            </label>
            <input
              type="number"
              name="standaardSpoedservice"
              value={formData.standaardSpoedservice}
              onChange={handleInputChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderStylingTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">Factuur Kleurthema</h3>
        
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Kleurthema
          </label>
          <select
            name="factuurKleurThema"
            value={formData.factuurKleurThema}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="blue">Blauw</option>
            <option value="green">Groen</option>
            <option value="red">Rood</option>
            <option value="purple">Paars</option>
            <option value="orange">Oranje</option>
            <option value="gray">Grijs</option>
          </select>
        </div>
      </div>

      <div className="p-4 bg-muted rounded-lg">
        <p className="text-sm text-muted-foreground">
          Het kleurthema wordt gebruikt voor de PDF facturen en bepaalt de hoofdkleur van headers, 
          lijnen en accenten in de factuur layout.
        </p>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Instellingen</h1>
          <p className="text-muted-foreground mt-1">
            Beheer uw bedrijfs- en factuurinstellingen
          </p>
        </div>
        
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-4 sm:mt-0"
        >
          <Save className="h-4 w-4" />
          <span>{isSaving ? 'Opslaan...' : 'Opslaan'}</span>
        </button>
      </div>

      {/* Save Message */}
      {saveMessage && (
        <div className={`p-4 rounded-lg ${
          saveMessage.includes('succesvol') 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {saveMessage}
        </div>
      )}

      {/* Tabs */}
      <div className="bg-card border border-border rounded-lg shadow-sm">
        <div className="border-b border-border">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'bedrijf' && renderBedrijfTab()}
          {activeTab === 'factuur' && renderFactuurTab()}
          {activeTab === 'tarieven' && renderTarievenTab()}
          {activeTab === 'styling' && renderStylingTab()}
        </div>
      </div>
    </div>
  );
}
