// Klant types
export interface Klant {
  id: string;
  type: 'particulier' | 'bedrijf';
  voornaam: string;
  achternaam: string;
  bedrijfsnaam?: string;
  adres: string;
  huisnummer: string;
  postcode: string;
  stad: string;
  telefoonnummer: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

// Tijdregistratie types
export interface Tijdregistratie {
  id: string;
  klantId: string;
  startTijd: Date;
  eindTijd?: Date;
  totaalTijd: number; // in seconden
  beschrijving?: string;
  status: 'actief' | 'gepauzeerd' | 'gestopt';
  createdAt: Date;
  updatedAt: Date;
}

// Wachtlijst types
export interface WachtlijstItem {
  id: string;
  klantId: string;
  tijdregistratieId: string;
  opgeslagenTijd: number; // in seconden
  beschrijving?: string;
  createdAt: Date;
}

// Service types
export type ServiceType = 'loodgieter' | 'elektricien' | 'klusjesman' | 'timmerman' | 'aannemer' | 'custom';

// Factuur types
export interface FactuurRegel {
  id: string;
  aantal: number;
  beschrijving: string;
  detailBeschrijving?: string;
  eenheid: string;
  uren: number;
  prijsPerUur: number;
  totaalExclBtw: number;
}

export interface ExtraKosten {
  voorrijkosten: number;
  spoedservice: number;
  parkeerkosten: number;
  materiaalkosten: number;
  materiaalDetails?: string;
}

export interface Factuur {
  id: string;
  factuurnummer: string;
  klantId: string;
  factuurdatum: Date;
  vervaldatum: Date;
  uitvoerdatum: Date;
  serviceType: ServiceType;
  customService?: string;
  betaaltermijn: number; // in dagen
  factuurregels: FactuurRegel[];
  extraKosten: ExtraKosten;
  kortingPercentage: number;
  kortingBedrag: number;
  subtotaal: number;
  btw21: number;
  btw9: number;
  totaalInclBtw: number;
  qrCode?: string;
  betaalLink?: string;
  status: 'concept' | 'verzonden' | 'betaald' | 'vervallen';
  tijdregistratieId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Snelle berekening types
export interface SnelleBerekening {
  klantId: string;
  serviceType: ServiceType;
  customService?: string;
  klusbeschrijving: string;
  uurtarief: number;
  uren: number;
  btw21Percentage: number;
  btw9Percentage: number;
  extraKosten: ExtraKosten;
  kortingPercentage: number;
}

// Bedrijfsinstellingen types
export interface BedrijfsInstellingen {
  bedrijfsnaam: string;
  adres: string;
  postcode: string;
  stad: string;
  telefoonnummer: string;
  email: string;
  website?: string;
  kvkNummer?: string;
  btwNummer?: string;
  ibanNummer?: string;
  logo?: string;
  factuurnummerPrefix: string;
  factuurnummerCounter: number;
  standaardUurtarief: number;
  standaardBtw21: number;
  standaardBtw9: number;
  standaardVoorrijkosten: number;
  standaardSpoedservice: number;
  standaardBetaaltermijn: number;
  garantieTekst: string;
  garantieDagen: number;
  betaalTekst: string;
  bedankTekst: string;
  algemeneVoorwaarden: string;
  factuurKleurThema: string;
  createdAt: Date;
  updatedAt: Date;
}

// Theme types
export type Theme = 'light' | 'dark';

// Navigation types
export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  children?: NavigationItem[];
}

// Context types
export interface AppContextType {
  klanten: Klant[];
  tijdregistraties: Tijdregistratie[];
  wachtlijst: WachtlijstItem[];
  facturen: Factuur[];
  bedrijfsInstellingen: BedrijfsInstellingen;
  
  // Klanten methods
  addKlant: (klant: Omit<Klant, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateKlant: (id: string, klant: Partial<Klant>) => void;
  deleteKlant: (id: string) => void;
  getKlant: (id: string) => Klant | undefined;
  
  // Tijdregistratie methods
  startTijdregistratie: (klantId: string, beschrijving?: string) => string;
  stopTijdregistratie: (id: string) => void;
  pauseTijdregistratie: (id: string) => void;
  resumeTijdregistratie: (id: string) => void;
  resetTijdregistratie: (id: string) => void;
  addTijdToRegistratie: (id: string, uren: number, minuten: number) => void;
  
  // Wachtlijst methods
  addToWachtlijst: (klantId: string, tijdregistratieId: string, beschrijving?: string) => void;
  removeFromWachtlijst: (id: string) => void;
  resumeFromWachtlijst: (id: string) => void;
  
  // Factuur methods
  createFactuur: (factuur: Omit<Factuur, 'id' | 'factuurnummer' | 'createdAt' | 'updatedAt'>) => string;
  updateFactuur: (id: string, factuur: Partial<Factuur>) => void;
  deleteFactuur: (id: string) => void;
  getFactuur: (id: string) => Factuur | undefined;
  generateFactuurnummer: () => string;
  
  // Instellingen methods
  updateBedrijfsInstellingen: (instellingen: Partial<BedrijfsInstellingen>) => void;
}

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

// Utility types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  loading: boolean;
}

// PDF types
export interface PDFOptions {
  filename: string;
  format: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

// Contact action types
export type ContactAction = 'whatsapp' | 'sms' | 'call' | 'route';

export interface ContactActionConfig {
  type: ContactAction;
  label: string;
  icon: string;
  action: (klant: Klant) => void;
}
