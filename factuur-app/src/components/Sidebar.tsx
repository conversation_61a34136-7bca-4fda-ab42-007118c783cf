'use client';

import React from 'react';
import Link from 'next/link';
import { useApp } from '@/contexts/AppContext';
import { 
  Home, 
  Users, 
  Clock, 
  FileText, 
  Calculator, 
  Settings, 
  ListTodo,
  X,
  ChevronRight
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  badge?: number;
}

export default function Sidebar({ isOpen, onClose, currentPath }: SidebarProps) {
  const { klanten, tijdregistraties, wachtlijst, facturen } = useApp();

  // Calculate badges
  const activeTijdregistraties = tijdregistraties.filter(t => t.status === 'actief').length;
  const conceptFacturen = facturen.filter(f => f.status === 'concept').length;

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      path: '/',
    },
    {
      id: 'klanten',
      label: 'Klanten',
      icon: Users,
      path: '/klanten',
      badge: klanten.length,
    },
    {
      id: 'tijdregistratie',
      label: 'Tijdregistratie',
      icon: Clock,
      path: '/tijdregistratie',
      badge: activeTijdregistraties > 0 ? activeTijdregistraties : undefined,
    },
    {
      id: 'wachtlijst',
      label: 'Wachtlijst',
      icon: ListTodo,
      path: '/wachtlijst',
      badge: wachtlijst.length > 0 ? wachtlijst.length : undefined,
    },
    {
      id: 'facturen',
      label: 'Facturen',
      icon: FileText,
      path: '/facturen',
      badge: conceptFacturen > 0 ? conceptFacturen : undefined,
    },
    {
      id: 'snelle-berekening',
      label: 'Snelle Berekening',
      icon: Calculator,
      path: '/snelle-berekening',
    },
    {
      id: 'instellingen',
      label: 'Instellingen',
      icon: Settings,
      path: '/instellingen',
    },
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === '/';
    }
    return currentPath.startsWith(path);
  };

  return (
    <>
      {/* Sidebar */}
      <aside className={`
        fixed top-16 left-0 z-50 h-[calc(100vh-4rem)] w-64 
        bg-background border-r border-border
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:z-auto
      `}>
        {/* Mobile Close Button */}
        <div className="flex items-center justify-between p-4 border-b border-border lg:hidden">
          <h2 className="text-lg font-semibold text-foreground">Menu</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            aria-label="Close menu"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);
            
            return (
              <Link
                key={item.id}
                href={item.path}
                onClick={onClose}
                className={`
                  flex items-center justify-between w-full p-3 rounded-lg
                  transition-all duration-200 ease-in-out group
                  ${active 
                    ? 'bg-primary text-primary-foreground shadow-sm' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                  }
                `}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`h-5 w-5 ${active ? 'text-primary-foreground' : ''}`} />
                  <span className="font-medium">{item.label}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {item.badge !== undefined && item.badge > 0 && (
                    <span className={`
                      px-2 py-1 text-xs font-medium rounded-full
                      ${active 
                        ? 'bg-primary-foreground text-primary' 
                        : 'bg-primary text-primary-foreground'
                      }
                    `}>
                      {item.badge}
                    </span>
                  )}
                  <ChevronRight className={`
                    h-4 w-4 transition-transform duration-200
                    ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}
                    group-hover:translate-x-1
                  `} />
                </div>
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Factuur App v1.0
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              © 2024 Alle rechten voorbehouden
            </p>
          </div>
        </div>
      </aside>
    </>
  );
}
