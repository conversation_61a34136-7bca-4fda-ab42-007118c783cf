'use client';

import React, { useContext } from 'react';
import { ThemeContext } from '@/contexts/ThemeContext';
import { useApp } from '@/contexts/AppContext';
import { Menu, Sun, Moon, FileText, Settings } from 'lucide-react';
import Link from 'next/link';

interface TopBarProps {
  onMenuClick: () => void;
}

export default function TopBar({ onMenuClick }: TopBarProps) {
  const themeContext = useContext(ThemeContext);
  const theme = themeContext?.theme || 'light';
  const toggleTheme = themeContext?.toggleTheme || (() => {});

  const { bedrijfsInstellingen } = useApp();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background border-b border-border shadow-sm">
      <div className="flex items-center justify-between h-16 px-4">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* <PERSON><PERSON> */}
          <button
            onClick={onMenuClick}
            className="p-2 rounded-lg hover:bg-accent transition-colors lg:hidden"
            aria-label="Toggle menu"
          >
            <Menu className="h-5 w-5" />
          </button>

          {/* Logo & Title */}
          <Link href="/" className="flex items-center space-x-3">
            {bedrijfsInstellingen.logo ? (
              <img
                src={bedrijfsInstellingen.logo}
                alt="Logo"
                className="h-8 w-8 object-contain"
              />
            ) : (
              <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-primary-foreground" />
              </div>
            )}
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold text-foreground">
                {bedrijfsInstellingen.bedrijfsnaam || 'Factuur App'}
              </h1>
            </div>
          </Link>
        </div>

        {/* Center Section - Hidden on mobile */}
        <div className="hidden md:flex items-center space-x-6">
          <nav className="flex items-center space-x-4">
            <Link
              href="/klanten"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Klanten
            </Link>
            <Link
              href="/tijdregistratie"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Tijdregistratie
            </Link>
            <Link
              href="/facturen"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Facturen
            </Link>
            <Link
              href="/wachtlijst"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Wachtlijst
            </Link>
          </nav>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
          >
            {theme === 'light' ? (
              <Moon className="h-5 w-5" />
            ) : (
              <Sun className="h-5 w-5" />
            )}
          </button>

          {/* Settings Button */}
          <Link
            href="/instellingen"
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            aria-label="Instellingen"
          >
            <Settings className="h-5 w-5" />
          </Link>

          {/* User Info - Hidden on mobile */}
          <div className="hidden sm:flex items-center space-x-3 ml-4 pl-4 border-l border-border">
            <div className="text-right">
              <p className="text-sm font-medium text-foreground">
                {bedrijfsInstellingen.bedrijfsnaam || 'Gebruiker'}
              </p>
              <p className="text-xs text-muted-foreground">
                {bedrijfsInstellingen.email || 'Geen email ingesteld'}
              </p>
            </div>
            <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-primary-foreground">
                {(bedrijfsInstellingen.bedrijfsnaam || 'U').charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
