'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  AppContextType, 
  Klant, 
  Tijdregistratie, 
  WachtlijstItem, 
  Factuur, 
  BedrijfsInstellingen 
} from '@/types';

const AppContext = createContext<AppContextType | undefined>(undefined);

// Default bedrijfsinstellingen
const defaultBedrijfsInstellingen: BedrijfsInstellingen = {
  bedrijfsnaam: '',
  adres: '',
  postcode: '',
  stad: '',
  telefoonnummer: '',
  email: '',
  website: '',
  kvkNummer: '',
  btwNummer: '',
  ibanNummer: '',
  logo: '',
  factuurnummerPrefix: 'F',
  factuurnummerCounter: 1,
  standaardUurtarief: 75.65,
  standaardBtw21: 21,
  standaardBtw9: 9,
  standaardVoorrijkosten: 25,
  standaardSpoedservice: 25,
  standaardBetaaltermijn: 7,
  garantieTekst: 'Op alle werkzaamheden geven wij garantie.',
  garantieDagen: 90,
  betaalTekst: 'Gelieve het factuurbedrag binnen de betalingstermijn over te maken.',
  bedankTekst: 'Bedankt voor uw vertrouwen in onze dienstverlening.',
  algemeneVoorwaarden: '',
  factuurKleurThema: 'blue',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [klanten, setKlanten] = useState<Klant[]>([]);
  const [tijdregistraties, setTijdregistraties] = useState<Tijdregistratie[]>([]);
  const [wachtlijst, setWachtlijst] = useState<WachtlijstItem[]>([]);
  const [facturen, setFacturen] = useState<Factuur[]>([]);
  const [bedrijfsInstellingen, setBedrijfsInstellingen] = useState<BedrijfsInstellingen>(defaultBedrijfsInstellingen);
  const [mounted, setMounted] = useState(false);

  // Load data from localStorage on mount
  useEffect(() => {
    setMounted(true);
    loadFromLocalStorage();
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    if (mounted) {
      saveToLocalStorage();
    }
  }, [klanten, tijdregistraties, wachtlijst, facturen, bedrijfsInstellingen, mounted]);

  const loadFromLocalStorage = () => {
    try {
      const savedKlanten = localStorage.getItem('factuur-app-klanten');
      const savedTijdregistraties = localStorage.getItem('factuur-app-tijdregistraties');
      const savedWachtlijst = localStorage.getItem('factuur-app-wachtlijst');
      const savedFacturen = localStorage.getItem('factuur-app-facturen');
      const savedInstellingen = localStorage.getItem('factuur-app-instellingen');

      if (savedKlanten) {
        setKlanten(JSON.parse(savedKlanten).map((k: any) => ({
          ...k,
          createdAt: new Date(k.createdAt),
          updatedAt: new Date(k.updatedAt),
        })));
      }

      if (savedTijdregistraties) {
        setTijdregistraties(JSON.parse(savedTijdregistraties).map((t: any) => ({
          ...t,
          startTijd: new Date(t.startTijd),
          eindTijd: t.eindTijd ? new Date(t.eindTijd) : undefined,
          createdAt: new Date(t.createdAt),
          updatedAt: new Date(t.updatedAt),
        })));
      }

      if (savedWachtlijst) {
        setWachtlijst(JSON.parse(savedWachtlijst).map((w: any) => ({
          ...w,
          createdAt: new Date(w.createdAt),
        })));
      }

      if (savedFacturen) {
        setFacturen(JSON.parse(savedFacturen).map((f: any) => ({
          ...f,
          factuurdatum: new Date(f.factuurdatum),
          vervaldatum: new Date(f.vervaldatum),
          uitvoerdatum: new Date(f.uitvoerdatum),
          createdAt: new Date(f.createdAt),
          updatedAt: new Date(f.updatedAt),
        })));
      }

      if (savedInstellingen) {
        setBedrijfsInstellingen({
          ...JSON.parse(savedInstellingen),
          createdAt: new Date(JSON.parse(savedInstellingen).createdAt),
          updatedAt: new Date(JSON.parse(savedInstellingen).updatedAt),
        });
      }
    } catch (error) {
      console.error('Error loading data from localStorage:', error);
    }
  };

  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('factuur-app-klanten', JSON.stringify(klanten));
      localStorage.setItem('factuur-app-tijdregistraties', JSON.stringify(tijdregistraties));
      localStorage.setItem('factuur-app-wachtlijst', JSON.stringify(wachtlijst));
      localStorage.setItem('factuur-app-facturen', JSON.stringify(facturen));
      localStorage.setItem('factuur-app-instellingen', JSON.stringify(bedrijfsInstellingen));
    } catch (error) {
      console.error('Error saving data to localStorage:', error);
    }
  };

  // Klanten methods
  const addKlant = (klantData: Omit<Klant, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newKlant: Klant = {
      ...klantData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setKlanten(prev => [...prev, newKlant]);
  };

  const updateKlant = (id: string, klantData: Partial<Klant>) => {
    setKlanten(prev => prev.map(klant => 
      klant.id === id 
        ? { ...klant, ...klantData, updatedAt: new Date() }
        : klant
    ));
  };

  const deleteKlant = (id: string) => {
    setKlanten(prev => prev.filter(klant => klant.id !== id));
    // Also remove related tijdregistraties and wachtlijst items
    setTijdregistraties(prev => prev.filter(t => t.klantId !== id));
    setWachtlijst(prev => prev.filter(w => w.klantId !== id));
  };

  const getKlant = (id: string) => {
    return klanten.find(klant => klant.id === id);
  };

  // Tijdregistratie methods
  const startTijdregistratie = (klantId: string, beschrijving?: string) => {
    const newTijdregistratie: Tijdregistratie = {
      id: generateId(),
      klantId,
      startTijd: new Date(),
      totaalTijd: 0,
      beschrijving,
      status: 'actief',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setTijdregistraties(prev => [...prev, newTijdregistratie]);
    return newTijdregistratie.id;
  };

  const stopTijdregistratie = (id: string) => {
    setTijdregistraties(prev => prev.map(t => {
      if (t.id === id && t.status === 'actief') {
        const eindTijd = new Date();
        const totaalTijd = Math.floor((eindTijd.getTime() - t.startTijd.getTime()) / 1000);
        return {
          ...t,
          eindTijd,
          totaalTijd,
          status: 'gestopt' as const,
          updatedAt: new Date(),
        };
      }
      return t;
    }));
  };

  const pauseTijdregistratie = (id: string) => {
    setTijdregistraties(prev => prev.map(t => 
      t.id === id 
        ? { ...t, status: 'gepauzeerd' as const, updatedAt: new Date() }
        : t
    ));
  };

  const resumeTijdregistratie = (id: string) => {
    setTijdregistraties(prev => prev.map(t => 
      t.id === id 
        ? { ...t, status: 'actief' as const, updatedAt: new Date() }
        : t
    ));
  };

  const resetTijdregistratie = (id: string) => {
    setTijdregistraties(prev => prev.map(t => 
      t.id === id 
        ? { 
            ...t, 
            startTijd: new Date(), 
            eindTijd: undefined,
            totaalTijd: 0, 
            status: 'actief' as const, 
            updatedAt: new Date() 
          }
        : t
    ));
  };

  const addTijdToRegistratie = (id: string, uren: number, minuten: number) => {
    const extraTijd = (uren * 3600) + (minuten * 60);
    setTijdregistraties(prev => prev.map(t => 
      t.id === id 
        ? { ...t, totaalTijd: t.totaalTijd + extraTijd, updatedAt: new Date() }
        : t
    ));
  };

  // Wachtlijst methods
  const addToWachtlijst = (klantId: string, tijdregistratieId: string, beschrijving?: string) => {
    const tijdregistratie = tijdregistraties.find(t => t.id === tijdregistratieId);
    if (!tijdregistratie) return;

    const wachtlijstItem: WachtlijstItem = {
      id: generateId(),
      klantId,
      tijdregistratieId,
      opgeslagenTijd: tijdregistratie.totaalTijd,
      beschrijving,
      createdAt: new Date(),
    };
    setWachtlijst(prev => [...prev, wachtlijstItem]);
  };

  const removeFromWachtlijst = (id: string) => {
    setWachtlijst(prev => prev.filter(w => w.id !== id));
  };

  const resumeFromWachtlijst = (id: string) => {
    const wachtlijstItem = wachtlijst.find(w => w.id === id);
    if (!wachtlijstItem) return;

    // Update tijdregistratie with saved time
    setTijdregistraties(prev => prev.map(t => 
      t.id === wachtlijstItem.tijdregistratieId
        ? { 
            ...t, 
            totaalTijd: wachtlijstItem.opgeslagenTijd,
            status: 'actief' as const,
            updatedAt: new Date() 
          }
        : t
    ));

    // Remove from wachtlijst
    removeFromWachtlijst(id);
  };

  // Factuur methods
  const generateFactuurnummer = () => {
    const year = new Date().getFullYear();
    const counter = bedrijfsInstellingen.factuurnummerCounter.toString().padStart(4, '0');
    return `${bedrijfsInstellingen.factuurnummerPrefix}-${year}-${counter}`;
  };

  const createFactuur = (factuurData: Omit<Factuur, 'id' | 'factuurnummer' | 'createdAt' | 'updatedAt'>) => {
    const newFactuur: Factuur = {
      ...factuurData,
      id: generateId(),
      factuurnummer: generateFactuurnummer(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setFacturen(prev => [...prev, newFactuur]);
    
    // Increment counter
    setBedrijfsInstellingen(prev => ({
      ...prev,
      factuurnummerCounter: prev.factuurnummerCounter + 1,
      updatedAt: new Date(),
    }));
    
    return newFactuur.id;
  };

  const updateFactuur = (id: string, factuurData: Partial<Factuur>) => {
    setFacturen(prev => prev.map(factuur => 
      factuur.id === id 
        ? { ...factuur, ...factuurData, updatedAt: new Date() }
        : factuur
    ));
  };

  const deleteFactuur = (id: string) => {
    setFacturen(prev => prev.filter(factuur => factuur.id !== id));
  };

  const getFactuur = (id: string) => {
    return facturen.find(factuur => factuur.id === id);
  };

  // Instellingen methods
  const updateBedrijfsInstellingen = (instellingen: Partial<BedrijfsInstellingen>) => {
    setBedrijfsInstellingen(prev => ({
      ...prev,
      ...instellingen,
      updatedAt: new Date(),
    }));
  };

  // Utility function to generate IDs
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  const value: AppContextType = {
    klanten,
    tijdregistraties,
    wachtlijst,
    facturen,
    bedrijfsInstellingen,
    addKlant,
    updateKlant,
    deleteKlant,
    getKlant,
    startTijdregistratie,
    stopTijdregistratie,
    pauseTijdregistratie,
    resumeTijdregistratie,
    resetTijdregistratie,
    addTijdToRegistratie,
    addToWachtlijst,
    removeFromWachtlijst,
    resumeFromWachtlijst,
    createFactuur,
    updateFactuur,
    deleteFactuur,
    getFactuur,
    generateFactuurnummer,
    updateBedrijfsInstellingen,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
