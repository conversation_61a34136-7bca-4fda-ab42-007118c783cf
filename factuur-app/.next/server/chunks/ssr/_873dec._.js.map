{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport {\n  Users,\n  Clock,\n  FileText,\n  TrendingUp,\n  Calendar,\n  Euro,\n  Activity,\n  AlertCircle\n} from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function Home() {\n  const { klanten, tijdregistraties, facturen, wachtlijst } = useApp();\n\n  // Calculate statistics\n  const stats = {\n    totalKlanten: klanten.length,\n    activeTijdregistraties: tijdregistraties.filter(t => t.status === 'actief').length,\n    conceptFacturen: facturen.filter(f => f.status === 'concept').length,\n    wachtlijstItems: wachtlijst.length,\n    totalFacturen: facturen.length,\n    totalOmzet: facturen\n      .filter(f => f.status === 'betaald')\n      .reduce((sum, f) => sum + f.totaalInclBtw, 0),\n    openstaandeFacturen: facturen\n      .filter(f => f.status === 'verzonden')\n      .reduce((sum, f) => sum + f.totaalInclBtw, 0),\n  };\n\n  const recenteActiviteiten = [\n    ...tijdregistraties\n      .filter(t => t.status === 'actief')\n      .slice(0, 3)\n      .map(t => ({\n        type: 'tijdregistratie',\n        klant: klanten.find(k => k.id === t.klantId),\n        tijd: t.createdAt,\n        beschrijving: 'Tijdregistratie actief'\n      })),\n    ...facturen\n      .filter(f => f.status === 'concept')\n      .slice(0, 2)\n      .map(f => ({\n        type: 'factuur',\n        klant: klanten.find(k => k.id === f.klantId),\n        tijd: f.createdAt,\n        beschrijving: 'Concept factuur'\n      }))\n  ].sort((a, b) => b.tijd.getTime() - a.tijd.getTime()).slice(0, 5);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Dashboard</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Overzicht van uw factuur activiteiten\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2 mt-4 sm:mt-0\">\n          <div className=\"text-sm text-muted-foreground\">\n            {new Date().toLocaleDateString('nl-NL', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Totaal Klanten</p>\n              <p className=\"text-2xl font-bold text-foreground\">{stats.totalKlanten}</p>\n            </div>\n            <Users className=\"h-8 w-8 text-primary\" />\n          </div>\n          <div className=\"mt-4\">\n            <Link\n              href=\"/klanten\"\n              className=\"text-sm text-primary hover:underline\"\n            >\n              Bekijk alle klanten →\n            </Link>\n          </div>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Actieve Tijdregistraties</p>\n              <p className=\"text-2xl font-bold text-foreground\">{stats.activeTijdregistraties}</p>\n            </div>\n            <Clock className=\"h-8 w-8 text-orange-500\" />\n          </div>\n          <div className=\"mt-4\">\n            <Link\n              href=\"/tijdregistratie\"\n              className=\"text-sm text-primary hover:underline\"\n            >\n              Ga naar tijdregistratie →\n            </Link>\n          </div>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Concept Facturen</p>\n              <p className=\"text-2xl font-bold text-foreground\">{stats.conceptFacturen}</p>\n            </div>\n            <FileText className=\"h-8 w-8 text-blue-500\" />\n          </div>\n          <div className=\"mt-4\">\n            <Link\n              href=\"/facturen\"\n              className=\"text-sm text-primary hover:underline\"\n            >\n              Bekijk facturen →\n            </Link>\n          </div>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Wachtlijst</p>\n              <p className=\"text-2xl font-bold text-foreground\">{stats.wachtlijstItems}</p>\n            </div>\n            <Activity className=\"h-8 w-8 text-purple-500\" />\n          </div>\n          <div className=\"mt-4\">\n            <Link\n              href=\"/wachtlijst\"\n              className=\"text-sm text-primary hover:underline\"\n            >\n              Bekijk wachtlijst →\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Financial Overview */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Totale Omzet</h3>\n            <TrendingUp className=\"h-5 w-5 text-green-500\" />\n          </div>\n          <p className=\"text-3xl font-bold text-green-600\">\n            €{stats.totalOmzet.toFixed(2)}\n          </p>\n          <p className=\"text-sm text-muted-foreground mt-2\">\n            Van {stats.totalFacturen} facturen\n          </p>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Openstaand</h3>\n            <Euro className=\"h-5 w-5 text-orange-500\" />\n          </div>\n          <p className=\"text-3xl font-bold text-orange-600\">\n            €{stats.openstaandeFacturen.toFixed(2)}\n          </p>\n          <p className=\"text-sm text-muted-foreground mt-2\">\n            Nog te ontvangen\n          </p>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Deze Maand</h3>\n            <Calendar className=\"h-5 w-5 text-blue-500\" />\n          </div>\n          <p className=\"text-3xl font-bold text-blue-600\">\n            €{facturen\n              .filter(f => {\n                const now = new Date();\n                const factuurDate = new Date(f.factuurdatum);\n                return factuurDate.getMonth() === now.getMonth() &&\n                       factuurDate.getFullYear() === now.getFullYear();\n              })\n              .reduce((sum, f) => sum + f.totaalInclBtw, 0)\n              .toFixed(2)\n            }\n          </p>\n          <p className=\"text-sm text-muted-foreground mt-2\">\n            Facturen deze maand\n          </p>\n        </div>\n      </div>\n\n      {/* Recent Activities */}\n      <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-4\">Recente Activiteiten</h3>\n        {recenteActiviteiten.length > 0 ? (\n          <div className=\"space-y-4\">\n            {recenteActiviteiten.map((activiteit, index) => (\n              <div key={index} className=\"flex items-center space-x-4 p-3 bg-muted rounded-lg\">\n                <div className={`p-2 rounded-full ${\n                  activiteit.type === 'tijdregistratie' ? 'bg-orange-100 text-orange-600' : 'bg-blue-100 text-blue-600'\n                }`}>\n                  {activiteit.type === 'tijdregistratie' ? (\n                    <Clock className=\"h-4 w-4\" />\n                  ) : (\n                    <FileText className=\"h-4 w-4\" />\n                  )}\n                </div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-foreground\">\n                    {activiteit.beschrijving}\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    {activiteit.klant ? `${activiteit.klant.voornaam} ${activiteit.klant.achternaam}` : 'Onbekende klant'} •\n                    {activiteit.tijd.toLocaleDateString('nl-NL')} om {activiteit.tijd.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-8\">\n            <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <p className=\"text-muted-foreground\">Geen recente activiteiten</p>\n            <p className=\"text-sm text-muted-foreground mt-2\">\n              Start met het toevoegen van klanten en tijdregistraties\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-4\">Snelle Acties</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Link\n            href=\"/klanten/nieuw\"\n            className=\"flex items-center justify-center p-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            <Users className=\"h-5 w-5 mr-2\" />\n            Nieuwe Klant\n          </Link>\n          <Link\n            href=\"/tijdregistratie\"\n            className=\"flex items-center justify-center p-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors\"\n          >\n            <Clock className=\"h-5 w-5 mr-2\" />\n            Start Tijdregistratie\n          </Link>\n          <Link\n            href=\"/snelle-berekening\"\n            className=\"flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n          >\n            <TrendingUp className=\"h-5 w-5 mr-2\" />\n            Snelle Berekening\n          </Link>\n          <Link\n            href=\"/facturen/nieuw\"\n            className=\"flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            <FileText className=\"h-5 w-5 mr-2\" />\n            Nieuwe Factuur\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAWA;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAEjE,uBAAuB;IACvB,MAAM,QAAQ;QACZ,cAAc,QAAQ,MAAM;QAC5B,wBAAwB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QAClF,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QACpE,iBAAiB,WAAW,MAAM;QAClC,eAAe,SAAS,MAAM;QAC9B,YAAY,SACT,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;QAC7C,qBAAqB,SAClB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;IAC/C;IAEA,MAAM,sBAAsB;WACvB,iBACA,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,IAAK,CAAC;gBACT,MAAM;gBACN,OAAO,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,OAAO;gBAC3C,MAAM,EAAE,SAAS;gBACjB,cAAc;YAChB,CAAC;WACA,SACA,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,IAAK,CAAC;gBACT,MAAM;gBACN,OAAO,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,OAAO;gBAC3C,MAAM,EAAE,SAAS;gBACjB,cAAc;YAChB,CAAC;KACJ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG;IAE/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;gCACtC,SAAS;gCACT,MAAM;gCACN,OAAO;gCACP,KAAK;4BACP;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsC,MAAM,YAAY;;;;;;;;;;;;kDAEvE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsC,MAAM,sBAAsB;;;;;;;;;;;;kDAEjF,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsC,MAAM,eAAe;;;;;;;;;;;;kDAE1E,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsC,MAAM,eAAe;;;;;;;;;;;;kDAE1E,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC;gCAAE,WAAU;;oCAAoC;oCAC7C,MAAM,UAAU,CAAC,OAAO,CAAC;;;;;;;0CAE7B,8OAAC;gCAAE,WAAU;;oCAAqC;oCAC3C,MAAM,aAAa;oCAAC;;;;;;;;;;;;;kCAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAElB,8OAAC;gCAAE,WAAU;;oCAAqC;oCAC9C,MAAM,mBAAmB,CAAC,OAAO,CAAC;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC;gCAAE,WAAU;;oCAAmC;oCAC5C,SACC,MAAM,CAAC,CAAA;wCACN,MAAM,MAAM,IAAI;wCAChB,MAAM,cAAc,IAAI,KAAK,EAAE,YAAY;wCAC3C,OAAO,YAAY,QAAQ,OAAO,IAAI,QAAQ,MACvC,YAAY,WAAW,OAAO,IAAI,WAAW;oCACtD,GACC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE,GAC1C,OAAO,CAAC;;;;;;;0CAGb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;oBAC1D,oBAAoB,MAAM,GAAG,kBAC5B,8OAAC;wBAAI,WAAU;kCACZ,oBAAoB,GAAG,CAAC,CAAC,YAAY,sBACpC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAW,CAAC,iBAAiB,EAChC,WAAW,IAAI,KAAK,oBAAoB,kCAAkC,6BAC1E;kDACC,WAAW,IAAI,KAAK,kCACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;iEAEjB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,WAAW,YAAY;;;;;;0DAE1B,8OAAC;gDAAE,WAAU;;oDACV,WAAW,KAAK,GAAG,GAAG,WAAW,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC,UAAU,EAAE,GAAG;oDAAkB;oDACrG,WAAW,IAAI,CAAC,kBAAkB,CAAC;oDAAS;oDAAK,WAAW,IAAI,CAAC,kBAAkB,CAAC,SAAS;wDAAE,MAAM;wDAAW,QAAQ;oDAAU;;;;;;;;;;;;;;+BAhB/H;;;;;;;;;6CAuBd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD"}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "file": "activity.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/activity.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2',\n      key: '169zse',\n    },\n  ],\n];\n\n/**\n * @component @name Activity\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTJoLTIuNDhhMiAyIDAgMCAwLTEuOTMgMS40NmwtMi4zNSA4LjM2YS4yNS4yNSAwIDAgMS0uNDggMEw5LjI0IDIuMThhLjI1LjI1IDAgMCAwLS40OCAwbC0yLjM1IDguMzZBMiAyIDAgMCAxIDQuNDkgMTJIMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/activity\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Activity = createLucideIcon('activity', __iconNode);\n\nexport default Activity;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "file": "euro.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/euro.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 10h12', key: '1y6xl8' }],\n  ['path', { d: 'M4 14h9', key: '1loblj' }],\n  [\n    'path',\n    {\n      d: 'M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2',\n      key: '1j6lzo',\n    },\n  ],\n];\n\n/**\n * @component @name Euro\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMGgxMiIgLz4KICA8cGF0aCBkPSJNNCAxNGg5IiAvPgogIDxwYXRoIGQ9Ik0xOSA2YTcuNyA3LjcgMCAwIDAtNS4yLTJBNy45IDcuOSAwIDAgMCA2IDEyYzAgNC40IDMuNSA4IDcuOCA4IDIgMCAzLjgtLjggNS4yLTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/euro\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Euro = createLucideIcon('euro', __iconNode);\n\nexport default Euro;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}