module.exports = {

"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("module", () => require("module"));

module.exports = mod;
}}),
"[project]/src/utils/berekeningPdfGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "generateBerekeningPDF": (()=>generateBerekeningPDF)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript)");
;
const generateBerekeningPDF = async (berekeningData)=>{
    const pdf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsPDF"]('p', 'mm', 'a4');
    const pageWidth = 210;
    const pageHeight = 297;
    // Colors
    const blueColor = [
        54,
        96,
        191
    ]; // #3660BF
    const lightBlueColor = [
        240,
        243,
        251
    ];
    const darkGrayColor = [
        51,
        51,
        51
    ];
    const lightGrayColor = [
        128,
        128,
        128
    ];
    // Helper function to add text with proper encoding
    const addText = (text, x, y, options = {})=>{
        pdf.setFont(options.font || 'helvetica', options.style || 'normal');
        pdf.setFontSize(options.size || 10);
        if (options.color) {
            pdf.setTextColor(...options.color);
        }
        pdf.text(text, x, y, options.align ? {
            align: options.align
        } : undefined);
    };
    // Colors matching the template
    const primaryBlue = [
        37,
        99,
        235
    ]; // #2563eb
    const darkBlue = [
        30,
        58,
        138
    ]; // #1e3a8a
    const lightBlue = [
        240,
        249,
        255
    ]; // #f0f9ff
    const gray50 = [
        249,
        250,
        251
    ]; // #f9fafb
    const gray600 = [
        75,
        85,
        99
    ]; // #4b5563
    const gray700 = [
        55,
        65,
        81
    ]; // #374151
    const gray800 = [
        31,
        41,
        55
    ]; // #1f2937
    const gray900 = [
        17,
        24,
        39
    ]; // #111827
    // Helper functions
    const addGradientRect = (x, y, width, height)=>{
        const steps = 20;
        for(let i = 0; i < steps; i++){
            const ratio = i / steps;
            const r = primaryBlue[0] + (darkBlue[0] - primaryBlue[0]) * ratio;
            const g = primaryBlue[1] + (darkBlue[1] - primaryBlue[1]) * ratio;
            const b = primaryBlue[2] + (darkBlue[2] - primaryBlue[2]) * ratio;
            pdf.setFillColor(r, g, b);
            pdf.rect(x, y + height * ratio, width, height / steps, 'F');
        }
    };
    // Header with gradient background
    addGradientRect(0, 0, pageWidth, 25);
    // KOSTENBEREKENING title in white
    addText('KOSTENBEREKENING', 15, 15, {
        color: [
            255,
            255,
            255
        ],
        style: 'bold',
        size: 18
    });
    // Contact info in header
    const contactY = 20;
    addText('📞 020-1234567', 15, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    addText('✉ <EMAIL>', 70, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    addText('🌐 www.klusexperts.nl', 140, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    // Company logo area (if available)
    if (berekeningData.bedrijfsInstellingen.logo) {
        // Logo would be added here - for now we'll add company name
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(14);
        pdf.text(berekeningData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 20, {
            align: 'right'
        });
    }
    // Reset text color
    pdf.setTextColor(...darkGrayColor);
    // Company details (right side)
    let yPos = 45;
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    const companyDetails = [
        berekeningData.bedrijfsInstellingen.bedrijfsnaam,
        berekeningData.bedrijfsInstellingen.adres,
        `${berekeningData.bedrijfsInstellingen.postcode} ${berekeningData.bedrijfsInstellingen.stad}`,
        berekeningData.bedrijfsInstellingen.telefoonnummer,
        berekeningData.bedrijfsInstellingen.email,
        berekeningData.bedrijfsInstellingen.website || ''
    ].filter(Boolean);
    companyDetails.forEach((detail, index)=>{
        pdf.text(detail, pageWidth - 15, yPos + index * 4, {
            align: 'right'
        });
    });
    // Customer details (left side)
    yPos = 45;
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(10);
    pdf.text('Klantgegevens:', 15, yPos);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    yPos += 6;
    const customerName = berekeningData.klant.type === 'bedrijf' && berekeningData.klant.bedrijfsnaam ? berekeningData.klant.bedrijfsnaam : `${berekeningData.klant.voornaam} ${berekeningData.klant.achternaam}`;
    const customerDetails = [
        customerName,
        berekeningData.klant.type === 'bedrijf' && berekeningData.klant.bedrijfsnaam ? `t.a.v. ${berekeningData.klant.voornaam} ${berekeningData.klant.achternaam}` : '',
        `${berekeningData.klant.adres} ${berekeningData.klant.huisnummer}`,
        `${berekeningData.klant.postcode} ${berekeningData.klant.stad}`
    ].filter(Boolean);
    customerDetails.forEach((detail, index)=>{
        pdf.text(detail, 15, yPos + index * 4);
    });
    // Service details box (right side)
    const boxX = pageWidth - 80;
    const boxY = 70;
    const boxWidth = 65;
    const boxHeight = 25;
    // Light blue background for service details
    pdf.setFillColor(...lightBlueColor);
    pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');
    // Service details
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.setTextColor(...darkGrayColor);
    const serviceLabel = berekeningData.serviceType === 'custom' ? berekeningData.customService : berekeningData.serviceType.charAt(0).toUpperCase() + berekeningData.serviceType.slice(1);
    const serviceDetails = [
        [
            'Service:',
            serviceLabel || ''
        ],
        [
            'Datum:',
            new Date().toLocaleDateString('nl-NL')
        ],
        [
            'Uren:',
            berekeningData.totalUren.toFixed(2)
        ],
        [
            'Tarief:',
            `€ ${berekeningData.uurtarief.toFixed(2)}`
        ]
    ];
    serviceDetails.forEach((detail, index)=>{
        pdf.text(detail[0], boxX + 2, boxY + 6 + index * 4);
        pdf.setFont('helvetica', 'normal');
        pdf.text(detail[1], boxX + 20, boxY + 6 + index * 4);
        pdf.setFont('helvetica', 'bold');
    });
    // Work description
    yPos = 105;
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(10);
    pdf.text('Werkzaamheden:', 15, yPos);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    yPos += 6;
    if (berekeningData.klusbeschrijving) {
        const descriptionLines = pdf.splitTextToSize(berekeningData.klusbeschrijving, pageWidth - 30);
        descriptionLines.forEach((line, index)=>{
            pdf.text(line, 15, yPos + index * 4);
        });
        yPos += descriptionLines.length * 4 + 10;
    } else {
        yPos += 10;
    }
    // Cost breakdown table
    const tableStartY = yPos;
    // Blue header background
    pdf.setFillColor(...blueColor);
    pdf.rect(15, yPos, pageWidth - 30, 8, 'F');
    // Table headers in white
    pdf.setTextColor(255, 255, 255);
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    const headers = [
        'Beschrijving',
        'Uren',
        'Tarief',
        'Bedrag'
    ];
    const colWidths = [
        80,
        30,
        30,
        40
    ];
    let xPos = 15;
    headers.forEach((header, index)=>{
        const headerX = index === 0 ? xPos + 2 : xPos + colWidths[index] - 2;
        const align = index === 0 ? undefined : {
            align: 'right'
        };
        pdf.text(header, headerX, yPos + 5, align);
        xPos += colWidths[index];
    });
    // Table rows
    yPos += 8;
    pdf.setTextColor(...darkGrayColor);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    const costRows = [
        [
            'Arbeidskosten',
            berekeningData.totalUren.toFixed(2),
            `€ ${berekeningData.uurtarief.toFixed(2)}`,
            `€ ${berekeningData.arbeidskosten.toFixed(2)}`
        ]
    ];
    // Add extra costs if any
    if (berekeningData.formData.voorrijkosten > 0) {
        costRows.push([
            'Voorrijkosten',
            '1',
            `€ ${berekeningData.formData.voorrijkosten.toFixed(2)}`,
            `€ ${berekeningData.formData.voorrijkosten.toFixed(2)}`
        ]);
    }
    if (berekeningData.formData.spoedservice > 0) {
        costRows.push([
            'Spoedservice',
            '1',
            `€ ${berekeningData.formData.spoedservice.toFixed(2)}`,
            `€ ${berekeningData.formData.spoedservice.toFixed(2)}`
        ]);
    }
    if (berekeningData.formData.parkeerkosten > 0) {
        costRows.push([
            'Parkeerkosten',
            '1',
            `€ ${berekeningData.formData.parkeerkosten.toFixed(2)}`,
            `€ ${berekeningData.formData.parkeerkosten.toFixed(2)}`
        ]);
    }
    if (berekeningData.formData.materiaalkosten > 0) {
        costRows.push([
            'Materiaalkosten',
            '1',
            `€ ${berekeningData.formData.materiaalkosten.toFixed(2)}`,
            `€ ${berekeningData.formData.materiaalkosten.toFixed(2)}`
        ]);
    }
    costRows.forEach((row, index)=>{
        const rowY = yPos + index * 6;
        // Alternating row colors
        if (index % 2 === 1) {
            pdf.setFillColor(248, 249, 250);
            pdf.rect(15, rowY, pageWidth - 30, 6, 'F');
        }
        xPos = 15;
        row.forEach((data, colIndex)=>{
            const align = colIndex === 0 ? 'left' : 'right';
            const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 : xPos + 2;
            pdf.text(data, textX, rowY + 3, align !== 'left' ? {
                align
            } : undefined);
            xPos += colWidths[colIndex];
        });
    });
    // Totals section
    const totalsY = yPos + costRows.length * 6 + 10;
    const totalsX = pageWidth - 80;
    // Totals background
    pdf.setFillColor(...lightBlueColor);
    pdf.rect(totalsX, totalsY, 65, 30, 'F');
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    const totals = [
        [
            'Subtotaal:',
            `€ ${berekeningData.subtotaal.toFixed(2)}`
        ],
        ...berekeningData.kortingBedrag > 0 ? [
            [
                'Korting:',
                `€ ${berekeningData.kortingBedrag.toFixed(2)}`
            ]
        ] : [],
        [
            'BTW 21%:',
            `€ ${berekeningData.btw21.toFixed(2)}`
        ],
        [
            '',
            ''
        ],
        [
            'Totaal incl. BTW:',
            `€ ${berekeningData.totaalInclBtw.toFixed(2)}`
        ]
    ];
    totals.forEach((total, index)=>{
        const isTotal = total[0] === 'Totaal incl. BTW:';
        if (isTotal) {
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(11);
        }
        if (total[0]) {
            pdf.text(total[0], totalsX + 2, totalsY + 6 + index * 5);
            pdf.text(total[1], totalsX + 63, totalsY + 6 + index * 5, {
                align: 'right'
            });
        }
        if (isTotal) {
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
        }
    });
    // Footer
    const footerY = pageHeight - 40;
    // Note
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.text('Opmerking:', 15, footerY);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    pdf.text('Dit is een kostenberekening en geen officiële factuur.', 15, footerY + 5);
    pdf.text('Voor een officiële factuur kunt u contact met ons opnemen.', 15, footerY + 10);
    // Company info
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    const footerInfo = [
        berekeningData.bedrijfsInstellingen.telefoonnummer ? `Tel: ${berekeningData.bedrijfsInstellingen.telefoonnummer}` : '',
        berekeningData.bedrijfsInstellingen.email ? `Email: ${berekeningData.bedrijfsInstellingen.email}` : '',
        berekeningData.bedrijfsInstellingen.website ? `Web: ${berekeningData.bedrijfsInstellingen.website}` : ''
    ].filter(Boolean);
    footerInfo.forEach((info, index)=>{
        pdf.text(info, 15, footerY + 20 + index * 4);
    });
    return pdf;
};
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__5eb9ab._.js.map