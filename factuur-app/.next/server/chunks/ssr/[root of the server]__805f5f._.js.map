{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/contexts/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  AppContextType, \n  Klant, \n  Tijdregistratie, \n  WachtlijstItem, \n  Factuur, \n  BedrijfsInstellingen \n} from '@/types';\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\n// Default bedrijfsinstellingen\nconst defaultBedrijfsInstellingen: BedrijfsInstellingen = {\n  bedrijfsnaam: '',\n  adres: '',\n  postcode: '',\n  stad: '',\n  telefoonnummer: '',\n  email: '',\n  website: '',\n  kvkNummer: '',\n  btwNummer: '',\n  ibanNummer: '',\n  logo: '',\n  factuurnummerPrefix: 'F',\n  factuurnummerCounter: 1,\n  standaardUurtarief: 75.65,\n  standaardBtw21: 21,\n  standaardBtw9: 9,\n  standaardVoorrijkosten: 25,\n  standaardSpoedservice: 25,\n  standaardBetaaltermijn: 7,\n  garantieTekst: 'Op alle werkzaamheden geven wij garantie.',\n  garantieDagen: 90,\n  betaalTekst: 'Gelieve het factuurbedrag binnen de betalingstermijn over te maken.',\n  bedankTekst: 'Bedankt voor uw vertrouwen in onze dienstverlening.',\n  algemeneVoorwaarden: '',\n  factuurKleurThema: 'blue',\n  createdAt: new Date(),\n  updatedAt: new Date(),\n};\n\nexport function AppProvider({ children }: { children: React.ReactNode }) {\n  const [klanten, setKlanten] = useState<Klant[]>([]);\n  const [tijdregistraties, setTijdregistraties] = useState<Tijdregistratie[]>([]);\n  const [wachtlijst, setWachtlijst] = useState<WachtlijstItem[]>([]);\n  const [facturen, setFacturen] = useState<Factuur[]>([]);\n  const [bedrijfsInstellingen, setBedrijfsInstellingen] = useState<BedrijfsInstellingen>(defaultBedrijfsInstellingen);\n  const [mounted, setMounted] = useState(false);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    setMounted(true);\n    loadFromLocalStorage();\n  }, []);\n\n  // Save to localStorage whenever data changes\n  useEffect(() => {\n    if (mounted) {\n      saveToLocalStorage();\n    }\n  }, [klanten, tijdregistraties, wachtlijst, facturen, bedrijfsInstellingen, mounted]);\n\n  const loadFromLocalStorage = () => {\n    try {\n      const savedKlanten = localStorage.getItem('factuur-app-klanten');\n      const savedTijdregistraties = localStorage.getItem('factuur-app-tijdregistraties');\n      const savedWachtlijst = localStorage.getItem('factuur-app-wachtlijst');\n      const savedFacturen = localStorage.getItem('factuur-app-facturen');\n      const savedInstellingen = localStorage.getItem('factuur-app-instellingen');\n\n      if (savedKlanten) {\n        setKlanten(JSON.parse(savedKlanten).map((k: any) => ({\n          ...k,\n          createdAt: new Date(k.createdAt),\n          updatedAt: new Date(k.updatedAt),\n        })));\n      }\n\n      if (savedTijdregistraties) {\n        setTijdregistraties(JSON.parse(savedTijdregistraties).map((t: any) => ({\n          ...t,\n          startTijd: new Date(t.startTijd),\n          eindTijd: t.eindTijd ? new Date(t.eindTijd) : undefined,\n          createdAt: new Date(t.createdAt),\n          updatedAt: new Date(t.updatedAt),\n        })));\n      }\n\n      if (savedWachtlijst) {\n        setWachtlijst(JSON.parse(savedWachtlijst).map((w: any) => ({\n          ...w,\n          createdAt: new Date(w.createdAt),\n        })));\n      }\n\n      if (savedFacturen) {\n        setFacturen(JSON.parse(savedFacturen).map((f: any) => ({\n          ...f,\n          factuurdatum: new Date(f.factuurdatum),\n          vervaldatum: new Date(f.vervaldatum),\n          uitvoerdatum: new Date(f.uitvoerdatum),\n          createdAt: new Date(f.createdAt),\n          updatedAt: new Date(f.updatedAt),\n        })));\n      }\n\n      if (savedInstellingen) {\n        setBedrijfsInstellingen({\n          ...JSON.parse(savedInstellingen),\n          createdAt: new Date(JSON.parse(savedInstellingen).createdAt),\n          updatedAt: new Date(JSON.parse(savedInstellingen).updatedAt),\n        });\n      }\n    } catch (error) {\n      console.error('Error loading data from localStorage:', error);\n    }\n  };\n\n  const saveToLocalStorage = () => {\n    try {\n      localStorage.setItem('factuur-app-klanten', JSON.stringify(klanten));\n      localStorage.setItem('factuur-app-tijdregistraties', JSON.stringify(tijdregistraties));\n      localStorage.setItem('factuur-app-wachtlijst', JSON.stringify(wachtlijst));\n      localStorage.setItem('factuur-app-facturen', JSON.stringify(facturen));\n      localStorage.setItem('factuur-app-instellingen', JSON.stringify(bedrijfsInstellingen));\n    } catch (error) {\n      console.error('Error saving data to localStorage:', error);\n    }\n  };\n\n  // Klanten methods\n  const addKlant = (klantData: Omit<Klant, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newKlant: Klant = {\n      ...klantData,\n      id: generateId(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setKlanten(prev => [...prev, newKlant]);\n  };\n\n  const updateKlant = (id: string, klantData: Partial<Klant>) => {\n    setKlanten(prev => prev.map(klant => \n      klant.id === id \n        ? { ...klant, ...klantData, updatedAt: new Date() }\n        : klant\n    ));\n  };\n\n  const deleteKlant = (id: string) => {\n    setKlanten(prev => prev.filter(klant => klant.id !== id));\n    // Also remove related tijdregistraties and wachtlijst items\n    setTijdregistraties(prev => prev.filter(t => t.klantId !== id));\n    setWachtlijst(prev => prev.filter(w => w.klantId !== id));\n  };\n\n  const getKlant = (id: string) => {\n    return klanten.find(klant => klant.id === id);\n  };\n\n  // Tijdregistratie methods\n  const startTijdregistratie = (klantId: string, beschrijving?: string) => {\n    const newTijdregistratie: Tijdregistratie = {\n      id: generateId(),\n      klantId,\n      startTijd: new Date(),\n      totaalTijd: 0,\n      beschrijving,\n      status: 'actief',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setTijdregistraties(prev => [...prev, newTijdregistratie]);\n    return newTijdregistratie.id;\n  };\n\n  const stopTijdregistratie = (id: string) => {\n    setTijdregistraties(prev => prev.map(t => {\n      if (t.id === id && t.status === 'actief') {\n        const eindTijd = new Date();\n        const totaalTijd = Math.floor((eindTijd.getTime() - t.startTijd.getTime()) / 1000);\n        return {\n          ...t,\n          eindTijd,\n          totaalTijd,\n          status: 'gestopt' as const,\n          updatedAt: new Date(),\n        };\n      }\n      return t;\n    }));\n  };\n\n  const pauseTijdregistratie = (id: string) => {\n    setTijdregistraties(prev => prev.map(t => \n      t.id === id \n        ? { ...t, status: 'gepauzeerd' as const, updatedAt: new Date() }\n        : t\n    ));\n  };\n\n  const resumeTijdregistratie = (id: string) => {\n    setTijdregistraties(prev => prev.map(t => \n      t.id === id \n        ? { ...t, status: 'actief' as const, updatedAt: new Date() }\n        : t\n    ));\n  };\n\n  const resetTijdregistratie = (id: string) => {\n    setTijdregistraties(prev => prev.map(t => \n      t.id === id \n        ? { \n            ...t, \n            startTijd: new Date(), \n            eindTijd: undefined,\n            totaalTijd: 0, \n            status: 'actief' as const, \n            updatedAt: new Date() \n          }\n        : t\n    ));\n  };\n\n  const addTijdToRegistratie = (id: string, uren: number, minuten: number) => {\n    const extraTijd = (uren * 3600) + (minuten * 60);\n    setTijdregistraties(prev => prev.map(t => \n      t.id === id \n        ? { ...t, totaalTijd: t.totaalTijd + extraTijd, updatedAt: new Date() }\n        : t\n    ));\n  };\n\n  // Wachtlijst methods\n  const addToWachtlijst = (klantId: string, tijdregistratieId: string, beschrijving?: string) => {\n    const tijdregistratie = tijdregistraties.find(t => t.id === tijdregistratieId);\n    if (!tijdregistratie) return;\n\n    const wachtlijstItem: WachtlijstItem = {\n      id: generateId(),\n      klantId,\n      tijdregistratieId,\n      opgeslagenTijd: tijdregistratie.totaalTijd,\n      beschrijving,\n      createdAt: new Date(),\n    };\n    setWachtlijst(prev => [...prev, wachtlijstItem]);\n  };\n\n  const removeFromWachtlijst = (id: string) => {\n    setWachtlijst(prev => prev.filter(w => w.id !== id));\n  };\n\n  const resumeFromWachtlijst = (id: string) => {\n    const wachtlijstItem = wachtlijst.find(w => w.id === id);\n    if (!wachtlijstItem) return;\n\n    // Update tijdregistratie with saved time\n    setTijdregistraties(prev => prev.map(t => \n      t.id === wachtlijstItem.tijdregistratieId\n        ? { \n            ...t, \n            totaalTijd: wachtlijstItem.opgeslagenTijd,\n            status: 'actief' as const,\n            updatedAt: new Date() \n          }\n        : t\n    ));\n\n    // Remove from wachtlijst\n    removeFromWachtlijst(id);\n  };\n\n  // Factuur methods\n  const generateFactuurnummer = () => {\n    const year = new Date().getFullYear();\n    const counter = bedrijfsInstellingen.factuurnummerCounter.toString().padStart(4, '0');\n    return `${bedrijfsInstellingen.factuurnummerPrefix}-${year}-${counter}`;\n  };\n\n  const createFactuur = (factuurData: Omit<Factuur, 'id' | 'factuurnummer' | 'createdAt' | 'updatedAt'>) => {\n    const newFactuur: Factuur = {\n      ...factuurData,\n      id: generateId(),\n      factuurnummer: generateFactuurnummer(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setFacturen(prev => [...prev, newFactuur]);\n    \n    // Increment counter\n    setBedrijfsInstellingen(prev => ({\n      ...prev,\n      factuurnummerCounter: prev.factuurnummerCounter + 1,\n      updatedAt: new Date(),\n    }));\n    \n    return newFactuur.id;\n  };\n\n  const updateFactuur = (id: string, factuurData: Partial<Factuur>) => {\n    setFacturen(prev => prev.map(factuur => \n      factuur.id === id \n        ? { ...factuur, ...factuurData, updatedAt: new Date() }\n        : factuur\n    ));\n  };\n\n  const deleteFactuur = (id: string) => {\n    setFacturen(prev => prev.filter(factuur => factuur.id !== id));\n  };\n\n  const getFactuur = (id: string) => {\n    return facturen.find(factuur => factuur.id === id);\n  };\n\n  // Instellingen methods\n  const updateBedrijfsInstellingen = (instellingen: Partial<BedrijfsInstellingen>) => {\n    setBedrijfsInstellingen(prev => ({\n      ...prev,\n      ...instellingen,\n      updatedAt: new Date(),\n    }));\n  };\n\n  // Utility function to generate IDs\n  const generateId = () => {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  };\n\n  const value: AppContextType = {\n    klanten,\n    tijdregistraties,\n    wachtlijst,\n    facturen,\n    bedrijfsInstellingen,\n    addKlant,\n    updateKlant,\n    deleteKlant,\n    getKlant,\n    startTijdregistratie,\n    stopTijdregistratie,\n    pauseTijdregistratie,\n    resumeTijdregistratie,\n    resetTijdregistratie,\n    addTijdToRegistratie,\n    addToWachtlijst,\n    removeFromWachtlijst,\n    resumeFromWachtlijst,\n    createFactuur,\n    updateFactuur,\n    deleteFactuur,\n    getFactuur,\n    generateFactuurnummer,\n    updateBedrijfsInstellingen,\n  };\n\n  return (\n    <AppContext.Provider value={value}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA8B;AAE7D,+BAA+B;AAC/B,MAAM,8BAAoD;IACxD,cAAc;IACd,OAAO;IACP,UAAU;IACV,MAAM;IACN,gBAAgB;IAChB,OAAO;IACP,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,MAAM;IACN,qBAAqB;IACrB,sBAAsB;IACtB,oBAAoB;IACpB,gBAAgB;IAChB,eAAe;IACf,wBAAwB;IACxB,uBAAuB;IACvB,wBAAwB;IACxB,eAAe;IACf,eAAe;IACf,aAAa;IACb,aAAa;IACb,qBAAqB;IACrB,mBAAmB;IACnB,WAAW,IAAI;IACf,WAAW,IAAI;AACjB;AAEO,SAAS,YAAY,EAAE,QAAQ,EAAiC;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX;IACF,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAS;QAAkB;QAAY;QAAU;QAAsB;KAAQ;IAEnF,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,MAAM,wBAAwB,aAAa,OAAO,CAAC;YACnD,MAAM,kBAAkB,aAAa,OAAO,CAAC;YAC7C,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,oBAAoB,aAAa,OAAO,CAAC;YAE/C,IAAI,cAAc;gBAChB,WAAW,KAAK,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,IAAW,CAAC;wBACnD,GAAG,CAAC;wBACJ,WAAW,IAAI,KAAK,EAAE,SAAS;wBAC/B,WAAW,IAAI,KAAK,EAAE,SAAS;oBACjC,CAAC;YACH;YAEA,IAAI,uBAAuB;gBACzB,oBAAoB,KAAK,KAAK,CAAC,uBAAuB,GAAG,CAAC,CAAC,IAAW,CAAC;wBACrE,GAAG,CAAC;wBACJ,WAAW,IAAI,KAAK,EAAE,SAAS;wBAC/B,UAAU,EAAE,QAAQ,GAAG,IAAI,KAAK,EAAE,QAAQ,IAAI;wBAC9C,WAAW,IAAI,KAAK,EAAE,SAAS;wBAC/B,WAAW,IAAI,KAAK,EAAE,SAAS;oBACjC,CAAC;YACH;YAEA,IAAI,iBAAiB;gBACnB,cAAc,KAAK,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,IAAW,CAAC;wBACzD,GAAG,CAAC;wBACJ,WAAW,IAAI,KAAK,EAAE,SAAS;oBACjC,CAAC;YACH;YAEA,IAAI,eAAe;gBACjB,YAAY,KAAK,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,IAAW,CAAC;wBACrD,GAAG,CAAC;wBACJ,cAAc,IAAI,KAAK,EAAE,YAAY;wBACrC,aAAa,IAAI,KAAK,EAAE,WAAW;wBACnC,cAAc,IAAI,KAAK,EAAE,YAAY;wBACrC,WAAW,IAAI,KAAK,EAAE,SAAS;wBAC/B,WAAW,IAAI,KAAK,EAAE,SAAS;oBACjC,CAAC;YACH;YAEA,IAAI,mBAAmB;gBACrB,wBAAwB;oBACtB,GAAG,KAAK,KAAK,CAAC,kBAAkB;oBAChC,WAAW,IAAI,KAAK,KAAK,KAAK,CAAC,mBAAmB,SAAS;oBAC3D,WAAW,IAAI,KAAK,KAAK,KAAK,CAAC,mBAAmB,SAAS;gBAC7D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAC3D,aAAa,OAAO,CAAC,gCAAgC,KAAK,SAAS,CAAC;YACpE,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAC9D,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC5D,aAAa,OAAO,CAAC,4BAA4B,KAAK,SAAS,CAAC;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,kBAAkB;IAClB,MAAM,WAAW,CAAC;QAChB,MAAM,WAAkB;YACtB,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,WAAW,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACxC;IAEA,MAAM,cAAc,CAAC,IAAY;QAC/B,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAC1B,MAAM,EAAE,KAAK,KACT;oBAAE,GAAG,KAAK;oBAAE,GAAG,SAAS;oBAAE,WAAW,IAAI;gBAAO,IAChD;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,4DAA4D;QAC5D,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAC3D,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IACvD;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,QAAQ,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC5C;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC,SAAiB;QAC7C,MAAM,qBAAsC;YAC1C,IAAI;YACJ;YACA,WAAW,IAAI;YACf,YAAY;YACZ;YACA,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,oBAAoB,CAAA,OAAQ;mBAAI;gBAAM;aAAmB;QACzD,OAAO,mBAAmB,EAAE;IAC9B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBACnC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,MAAM,KAAK,UAAU;oBACxC,MAAM,WAAW,IAAI;oBACrB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,SAAS,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI;oBAC7E,OAAO;wBACL,GAAG,CAAC;wBACJ;wBACA;wBACA,QAAQ;wBACR,WAAW,IAAI;oBACjB;gBACF;gBACA,OAAO;YACT;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,KACL;oBAAE,GAAG,CAAC;oBAAE,QAAQ;oBAAuB,WAAW,IAAI;gBAAO,IAC7D;IAER;IAEA,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,KACL;oBAAE,GAAG,CAAC;oBAAE,QAAQ;oBAAmB,WAAW,IAAI;gBAAO,IACzD;IAER;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,KACL;oBACE,GAAG,CAAC;oBACJ,WAAW,IAAI;oBACf,UAAU;oBACV,YAAY;oBACZ,QAAQ;oBACR,WAAW,IAAI;gBACjB,IACA;IAER;IAEA,MAAM,uBAAuB,CAAC,IAAY,MAAc;QACtD,MAAM,YAAY,AAAC,OAAO,OAAS,UAAU;QAC7C,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,KACL;oBAAE,GAAG,CAAC;oBAAE,YAAY,EAAE,UAAU,GAAG;oBAAW,WAAW,IAAI;gBAAO,IACpE;IAER;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC,SAAiB,mBAA2B;QACnE,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5D,IAAI,CAAC,iBAAiB;QAEtB,MAAM,iBAAiC;YACrC,IAAI;YACJ;YACA;YACA,gBAAgB,gBAAgB,UAAU;YAC1C;YACA,WAAW,IAAI;QACjB;QACA,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAe;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAClD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,iBAAiB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,CAAC,gBAAgB;QAErB,yCAAyC;QACzC,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,eAAe,iBAAiB,GACrC;oBACE,GAAG,CAAC;oBACJ,YAAY,eAAe,cAAc;oBACzC,QAAQ;oBACR,WAAW,IAAI;gBACjB,IACA;QAGN,yBAAyB;QACzB,qBAAqB;IACvB;IAEA,kBAAkB;IAClB,MAAM,wBAAwB;QAC5B,MAAM,OAAO,IAAI,OAAO,WAAW;QACnC,MAAM,UAAU,qBAAqB,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG;QACjF,OAAO,GAAG,qBAAqB,mBAAmB,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS;IACzE;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAsB;YAC1B,GAAG,WAAW;YACd,IAAI;YACJ,eAAe;YACf,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;QAEzC,oBAAoB;QACpB,wBAAwB,CAAA,OAAQ,CAAC;gBAC/B,GAAG,IAAI;gBACP,sBAAsB,KAAK,oBAAoB,GAAG;gBAClD,WAAW,IAAI;YACjB,CAAC;QAED,OAAO,WAAW,EAAE;IACtB;IAEA,MAAM,gBAAgB,CAAC,IAAY;QACjC,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,KACX;oBAAE,GAAG,OAAO;oBAAE,GAAG,WAAW;oBAAE,WAAW,IAAI;gBAAO,IACpD;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,uBAAuB;IACvB,MAAM,6BAA6B,CAAC;QAClC,wBAAwB,CAAA,OAAQ,CAAC;gBAC/B,GAAG,IAAI;gBACP,GAAG,YAAY;gBACf,WAAW,IAAI;YACjB,CAAC;IACH;IAEA,mCAAmC;IACnC,MAAM,aAAa;QACjB,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA,MAAM,QAAwB;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,WAAW,QAAQ;QAAC,OAAO;kBACzB;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { Theme, ThemeContextType } from '@/types';\n\nexport const ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setThemeState] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Hydration fix\n  useEffect(() => {\n    setMounted(true);\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setThemeState(savedTheme);\n      applyTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      setThemeState(systemTheme);\n      applyTheme(systemTheme);\n    }\n  }, []);\n\n  const applyTheme = (newTheme: Theme) => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.setAttribute('data-theme', 'dark');\n    } else {\n      root.removeAttribute('data-theme');\n    }\n  };\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    localStorage.setItem('theme', newTheme);\n    applyTheme(newTheme);\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return <div style={{ visibility: 'hidden' }}>{children}</div>;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAKO,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEjE,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,cAAc;YACd,WAAW;QACb,OAAO;YACL,0BAA0B;YAC1B,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,cAAc;YACd,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,YAAY,CAAC,cAAc;QAClC,OAAO;YACL,KAAK,eAAe,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,cAAc;QACd,aAAa,OAAO,CAAC,SAAS;QAC9B,WAAW;IACb;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;IACX;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,OAAO;gBAAE,YAAY;YAAS;sBAAI;;;;;;IAChD;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAa;QAAS;kBAC1D;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/components/TopBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { ThemeContext } from '@/contexts/ThemeContext';\nimport { useApp } from '@/contexts/AppContext';\nimport { Menu, Sun, Moon, FileText, Settings } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface TopBarProps {\n  onMenuClick: () => void;\n}\n\nexport default function TopBar({ onMenuClick }: TopBarProps) {\n  const themeContext = useContext(ThemeContext);\n  const theme = themeContext?.theme || 'light';\n  const toggleTheme = themeContext?.toggleTheme || (() => {});\n\n  let bedrijfsInstellingen;\n  try {\n    bedrijfsInstellingen = useApp().bedrijfsInstellingen;\n  } catch (error) {\n    // Fallback if AppProvider is not available\n    bedrijfsInstellingen = {\n      bedrijfsnaam: 'Factuur App',\n      email: '',\n      logo: ''\n    };\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-background border-b border-border shadow-sm\">\n      <div className=\"flex items-center justify-between h-16 px-4\">\n        {/* Left Section */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Menu Button */}\n          <button\n            onClick={onMenuClick}\n            className=\"p-2 rounded-lg hover:bg-accent transition-colors lg:hidden\"\n            aria-label=\"Toggle menu\"\n          >\n            <Menu className=\"h-5 w-5\" />\n          </button>\n\n          {/* Logo & Title */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            {bedrijfsInstellingen.logo ? (\n              <img\n                src={bedrijfsInstellingen.logo}\n                alt=\"Logo\"\n                className=\"h-8 w-8 object-contain\"\n              />\n            ) : (\n              <div className=\"h-8 w-8 bg-primary rounded-lg flex items-center justify-center\">\n                <FileText className=\"h-5 w-5 text-primary-foreground\" />\n              </div>\n            )}\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-lg font-semibold text-foreground\">\n                {bedrijfsInstellingen.bedrijfsnaam || 'Factuur App'}\n              </h1>\n            </div>\n          </Link>\n        </div>\n\n        {/* Center Section - Hidden on mobile */}\n        <div className=\"hidden md:flex items-center space-x-6\">\n          <nav className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/klanten\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Klanten\n            </Link>\n            <Link\n              href=\"/tijdregistratie\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Tijdregistratie\n            </Link>\n            <Link\n              href=\"/facturen\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Facturen\n            </Link>\n            <Link\n              href=\"/wachtlijst\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Wachtlijst\n            </Link>\n          </nav>\n        </div>\n\n        {/* Right Section */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Theme Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"p-2 rounded-lg hover:bg-accent transition-colors\"\n            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n          >\n            {theme === 'light' ? (\n              <Moon className=\"h-5 w-5\" />\n            ) : (\n              <Sun className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Settings Button */}\n          <Link\n            href=\"/instellingen\"\n            className=\"p-2 rounded-lg hover:bg-accent transition-colors\"\n            aria-label=\"Instellingen\"\n          >\n            <Settings className=\"h-5 w-5\" />\n          </Link>\n\n          {/* User Info - Hidden on mobile */}\n          <div className=\"hidden sm:flex items-center space-x-3 ml-4 pl-4 border-l border-border\">\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-foreground\">\n                {bedrijfsInstellingen.bedrijfsnaam || 'Gebruiker'}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                {bedrijfsInstellingen.email || 'Geen email ingesteld'}\n              </p>\n            </div>\n            <div className=\"h-8 w-8 bg-primary rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-primary-foreground\">\n                {(bedrijfsInstellingen.bedrijfsnaam || 'U').charAt(0).toUpperCase()}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;AAYe,SAAS,OAAO,EAAE,WAAW,EAAe;IACzD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,eAAY;IAC5C,MAAM,QAAQ,cAAc,SAAS;IACrC,MAAM,cAAc,cAAc,eAAe,CAAC,KAAO,CAAC;IAE1D,IAAI;IACJ,IAAI;QACF,uBAAuB,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD,IAAI,oBAAoB;IACtD,EAAE,OAAO,OAAO;QACd,2CAA2C;QAC3C,uBAAuB;YACrB,cAAc;YACd,OAAO;YACP,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;gCACtB,qBAAqB,IAAI,iBACxB,8OAAC;oCACC,KAAK,qBAAqB,IAAI;oCAC9B,KAAI;oCACJ,WAAU;;;;;yDAGZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,qBAAqB,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;8BAO9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,CAAC,UAAU,EAAE,UAAU,UAAU,SAAS,QAAQ,KAAK,CAAC;sCAEnE,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;qDAEhB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAKnB,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,qBAAqB,YAAY,IAAI;;;;;;sDAExC,8OAAC;4CAAE,WAAU;sDACV,qBAAqB,KAAK,IAAI;;;;;;;;;;;;8CAGnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,CAAC,qBAAqB,YAAY,IAAI,GAAG,EAAE,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useApp } from '@/contexts/AppContext';\nimport { \n  Home, \n  Users, \n  Clock, \n  FileText, \n  Calculator, \n  Settings, \n  ListTodo,\n  X,\n  ChevronRight\n} from 'lucide-react';\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n  currentPath: string;\n}\n\ninterface NavigationItem {\n  id: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  path: string;\n  badge?: number;\n}\n\nexport default function Sidebar({ isOpen, onClose, currentPath }: SidebarProps) {\n  const { klanten, tijdregistraties, wachtlijst, facturen } = useApp();\n\n  // Calculate badges\n  const activeTijdregistraties = tijdregistraties.filter(t => t.status === 'actief').length;\n  const conceptFacturen = facturen.filter(f => f.status === 'concept').length;\n\n  const navigationItems: NavigationItem[] = [\n    {\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: Home,\n      path: '/',\n    },\n    {\n      id: 'klanten',\n      label: 'Klanten',\n      icon: Users,\n      path: '/klanten',\n      badge: klanten.length,\n    },\n    {\n      id: 'tijdregistratie',\n      label: 'Tijdregistratie',\n      icon: Clock,\n      path: '/tijdregistratie',\n      badge: activeTijdregistraties > 0 ? activeTijdregistraties : undefined,\n    },\n    {\n      id: 'wachtlijst',\n      label: 'Wachtlijst',\n      icon: ListTodo,\n      path: '/wachtlijst',\n      badge: wachtlijst.length > 0 ? wachtlijst.length : undefined,\n    },\n    {\n      id: 'facturen',\n      label: 'Facturen',\n      icon: FileText,\n      path: '/facturen',\n      badge: conceptFacturen > 0 ? conceptFacturen : undefined,\n    },\n    {\n      id: 'snelle-berekening',\n      label: 'Snelle Berekening',\n      icon: Calculator,\n      path: '/snelle-berekening',\n    },\n    {\n      id: 'instellingen',\n      label: 'Instellingen',\n      icon: Settings,\n      path: '/instellingen',\n    },\n  ];\n\n  const isActive = (path: string) => {\n    if (path === '/') {\n      return currentPath === '/';\n    }\n    return currentPath.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Sidebar */}\n      <aside className={`\n        fixed top-16 left-0 z-50 h-[calc(100vh-4rem)] w-64 \n        bg-background border-r border-border\n        transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n        lg:translate-x-0 lg:static lg:z-auto\n      `}>\n        {/* Mobile Close Button */}\n        <div className=\"flex items-center justify-between p-4 border-b border-border lg:hidden\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Menu</h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-lg hover:bg-accent transition-colors\"\n            aria-label=\"Close menu\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"p-4 space-y-2\">\n          {navigationItems.map((item) => {\n            const Icon = item.icon;\n            const active = isActive(item.path);\n            \n            return (\n              <Link\n                key={item.id}\n                href={item.path}\n                onClick={onClose}\n                className={`\n                  flex items-center justify-between w-full p-3 rounded-lg\n                  transition-all duration-200 ease-in-out group\n                  ${active \n                    ? 'bg-primary text-primary-foreground shadow-sm' \n                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'\n                  }\n                `}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <Icon className={`h-5 w-5 ${active ? 'text-primary-foreground' : ''}`} />\n                  <span className=\"font-medium\">{item.label}</span>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  {item.badge !== undefined && item.badge > 0 && (\n                    <span className={`\n                      px-2 py-1 text-xs font-medium rounded-full\n                      ${active \n                        ? 'bg-primary-foreground text-primary' \n                        : 'bg-primary text-primary-foreground'\n                      }\n                    `}>\n                      {item.badge}\n                    </span>\n                  )}\n                  <ChevronRight className={`\n                    h-4 w-4 transition-transform duration-200\n                    ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}\n                    group-hover:translate-x-1\n                  `} />\n                </div>\n              </Link>\n            );\n          })}\n        </nav>\n\n        {/* Footer */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t border-border\">\n          <div className=\"text-center\">\n            <p className=\"text-xs text-muted-foreground\">\n              Factuur App v1.0\n            </p>\n            <p className=\"text-xs text-muted-foreground mt-1\">\n              © 2024 Alle rechten voorbehouden\n            </p>\n          </div>\n        </div>\n      </aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AA+Be,SAAS,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAgB;IAC5E,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAEjE,mBAAmB;IACnB,MAAM,yBAAyB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IACzF,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAE3E,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,OAAO;YACP,MAAM,mMAAA,CAAA,OAAI;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,MAAM;YACN,OAAO,QAAQ,MAAM;QACvB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,MAAM;YACN,OAAO,yBAAyB,IAAI,yBAAyB;QAC/D;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,MAAM;YACN,OAAO,WAAW,MAAM,GAAG,IAAI,WAAW,MAAM,GAAG;QACrD;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,MAAM;YACN,OAAO,kBAAkB,IAAI,kBAAkB;QACjD;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8MAAA,CAAA,aAAU;YAChB,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,MAAM;QACR;KACD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,gBAAgB;QACzB;QACA,OAAO,YAAY,UAAU,CAAC;IAChC;IAEA,qBACE;kBAEE,cAAA,8OAAC;YAAM,WAAW,CAAC;;;;QAIjB,EAAE,SAAS,kBAAkB,oBAAoB;;MAEnD,CAAC;;8BAEC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,SAAS,SAAS,KAAK,IAAI;wBAEjC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,SAAS;4BACT,WAAW,CAAC;;;kBAGV,EAAE,SACE,iDACA,8DACH;gBACH,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,SAAS,4BAA4B,IAAI;;;;;;sDACrE,8OAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,GAAG,mBACxC,8OAAC;4CAAK,WAAW,CAAC;;sBAEhB,EAAE,SACE,uCACA,qCACH;oBACH,CAAC;sDACE,KAAK,KAAK;;;;;;sDAGf,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAW,CAAC;;oBAExB,EAAE,SAAS,4BAA4B,oDAAoD;;kBAE7F,CAAC;;;;;;;;;;;;;2BAjCE,KAAK,EAAE;;;;;oBAqClB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAG7C,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D"}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport TopBar from './TopBar';\nimport Sidebar from './Sidebar';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const pathname = usePathname();\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const closeSidebar = () => {\n    setSidebarOpen(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Top Bar */}\n      <TopBar onMenuClick={toggleSidebar} />\n      \n      <div className=\"flex\">\n        {/* Sidebar */}\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={closeSidebar}\n          currentPath={pathname}\n        />\n        \n        {/* Main Content */}\n        <main className={`\n          flex-1 transition-all duration-300 ease-in-out\n          ${sidebarOpen ? 'lg:ml-64' : 'ml-0'}\n          pt-16\n        `}>\n          <div className=\"container mx-auto px-4 py-6 max-w-7xl\">\n            {children}\n          </div>\n        </main>\n      </div>\n      \n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={closeSidebar}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4HAAA,CAAA,UAAM;gBAAC,aAAa;;;;;;0BAErB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,6HAAA,CAAA,UAAO;wBACN,QAAQ;wBACR,SAAS;wBACT,aAAa;;;;;;kCAIf,8OAAC;wBAAK,WAAW,CAAC;;UAEhB,EAAE,cAAc,aAAa,OAAO;;QAEtC,CAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;YAMN,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;;;;;;;AAKnB"}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}