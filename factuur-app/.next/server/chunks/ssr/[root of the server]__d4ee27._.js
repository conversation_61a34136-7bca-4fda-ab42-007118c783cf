module.exports = {

"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("module", () => require("module"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/utils/pdfGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "generateProfessionalPDF": (()=>generateProfessionalPDF)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/qrcode/lib/index.js [app-ssr] (ecmascript)");
;
;
const generateProfessionalPDF = async (factuurData)=>{
    const pdf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsPDF"]('p', 'mm', 'a4');
    const pageWidth = 210;
    const pageHeight = 297;
    // Colors matching the template
    const primaryBlue = [
        37,
        99,
        235
    ]; // #2563eb
    const darkBlue = [
        30,
        58,
        138
    ]; // #1e3a8a
    const lightBlue = [
        240,
        249,
        255
    ]; // #f0f9ff
    const gray50 = [
        249,
        250,
        251
    ]; // #f9fafb
    const gray100 = [
        243,
        244,
        246
    ]; // #f3f4f6
    const gray600 = [
        75,
        85,
        99
    ]; // #4b5563
    const gray700 = [
        55,
        65,
        81
    ]; // #374151
    const gray800 = [
        31,
        41,
        55
    ]; // #1f2937
    const gray900 = [
        17,
        24,
        39
    ]; // #111827
    // Helper functions
    const addText = (text, x, y, options = {})=>{
        pdf.setFont(options.font || 'helvetica', options.style || 'normal');
        pdf.setFontSize(options.size || 10);
        if (options.color) {
            pdf.setTextColor(...options.color);
        } else {
            pdf.setTextColor(0, 0, 0);
        }
        pdf.text(text, x, y, options.align ? {
            align: options.align
        } : undefined);
    };
    const addGradientRect = (x, y, width, height)=>{
        // Create gradient effect with multiple rectangles
        const steps = 20;
        for(let i = 0; i < steps; i++){
            const ratio = i / steps;
            const r = primaryBlue[0] + (darkBlue[0] - primaryBlue[0]) * ratio;
            const g = primaryBlue[1] + (darkBlue[1] - primaryBlue[1]) * ratio;
            const b = primaryBlue[2] + (darkBlue[2] - primaryBlue[2]) * ratio;
            pdf.setFillColor(r, g, b);
            pdf.rect(x, y + height * ratio, width, height / steps, 'F');
        }
    };
    // Header with gradient background
    addGradientRect(0, 0, pageWidth, 25);
    // FACTUUR title in white with gradient effect
    pdf.setTextColor(255, 255, 255);
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(18);
    addText('FACTUUR', 15, 15, {
        color: [
            255,
            255,
            255
        ],
        style: 'bold',
        size: 18
    });
    // Contact info in header
    pdf.setFontSize(8);
    const contactY = 20;
    addText('📞 020-1234567', 15, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    addText('✉ <EMAIL>', 70, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    addText('🌐 www.klusexperts.nl', 140, contactY, {
        color: [
            255,
            255,
            255
        ],
        size: 8
    });
    // Company logo area (right side)
    if (factuurData.bedrijfsInstellingen.bedrijfsnaam) {
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(12);
        addText(factuurData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 15, {
            color: [
                255,
                255,
                255
            ],
            style: 'bold',
            size: 12,
            align: 'right'
        });
    }
    // Company and Client Info Section (3 columns)
    let yPos = 35;
    // Column 1: Klantgegevens
    addText('Klantgegevens', 15, yPos, {
        style: 'bold',
        size: 10,
        color: gray800
    });
    yPos += 6;
    const customerName = factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam ? factuurData.klant.bedrijfsnaam : `${factuurData.klant.voornaam} ${factuurData.klant.achternaam}`;
    const customerDetails = [
        customerName,
        factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam ? `t.a.v. ${factuurData.klant.voornaam} ${factuurData.klant.achternaam}` : '',
        `${factuurData.klant.adres} ${factuurData.klant.huisnummer}`,
        `${factuurData.klant.postcode} ${factuurData.klant.stad}`,
        factuurData.klant.kvkNummer ? `KVK: ${factuurData.klant.kvkNummer}` : '',
        factuurData.klant.btwNummer ? `BTW: ${factuurData.klant.btwNummer}` : '',
        factuurData.klant.telefoonnummer ? `Tel: ${factuurData.klant.telefoonnummer}` : ''
    ].filter(Boolean);
    customerDetails.forEach((detail, index)=>{
        const isBold = index === 0;
        addText(detail, 15, yPos + index * 4, {
            style: isBold ? 'bold' : 'normal',
            size: 9,
            color: isBold ? gray900 : gray600
        });
    });
    // Column 2: Bedrijfsgegevens
    const col2X = 75;
    yPos = 35;
    addText('Bedrijfsgegevens', col2X, yPos, {
        style: 'bold',
        size: 10,
        color: gray800
    });
    yPos += 6;
    const companyDetails = [
        factuurData.bedrijfsInstellingen.bedrijfsnaam,
        factuurData.bedrijfsInstellingen.contactpersoon ? `t.n.v. ${factuurData.bedrijfsInstellingen.contactpersoon}` : '',
        factuurData.bedrijfsInstellingen.adres,
        `${factuurData.bedrijfsInstellingen.postcode} ${factuurData.bedrijfsInstellingen.stad}`,
        factuurData.bedrijfsInstellingen.kvkNummer ? `KVK: ${factuurData.bedrijfsInstellingen.kvkNummer}` : '',
        factuurData.bedrijfsInstellingen.btwNummer ? `BTW: ${factuurData.bedrijfsInstellingen.btwNummer}` : '',
        factuurData.bedrijfsInstellingen.ibanNummer ? `IBAN: ${factuurData.bedrijfsInstellingen.ibanNummer}` : ''
    ].filter(Boolean);
    companyDetails.forEach((detail, index)=>{
        const isBold = index === 0;
        addText(detail, col2X, yPos + index * 4, {
            style: isBold ? 'bold' : 'normal',
            size: 9,
            color: isBold ? gray900 : gray600
        });
    });
    // Column 3: Factuurdetails box (right side)
    const boxX = 140;
    const boxY = 35;
    const boxWidth = 65;
    const boxHeight = 45;
    // Light gray background for invoice details
    pdf.setFillColor(...gray50);
    pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');
    // Invoice details in 2x3 grid
    const invoiceDetails = [
        [
            'Factuurnummer',
            factuurData.factuurnummer
        ],
        [
            'Factuurdatum',
            factuurData.factuurdatum.toLocaleDateString('nl-NL')
        ],
        [
            'Vervaldatum',
            factuurData.vervaldatum.toLocaleDateString('nl-NL')
        ],
        [
            'Uitgevoerd op',
            factuurData.uitvoerdatum.toLocaleDateString('nl-NL')
        ],
        [
            'Service',
            'Loodgieter'
        ],
        [
            'Betaaltermijn',
            '15 dagen'
        ]
    ];
    invoiceDetails.forEach((detail, index)=>{
        const row = Math.floor(index / 2);
        const col = index % 2;
        const x = boxX + 2 + col * 30;
        const y = boxY + 6 + row * 12;
        addText(detail[0], x, y, {
            size: 8,
            color: gray600
        });
        addText(detail[1], x, y + 4, {
            style: 'bold',
            size: 8,
            color: gray900
        });
    });
    // Services Table
    yPos = 90;
    // Light blue background for table container
    pdf.setFillColor(...lightBlue);
    pdf.rect(15, yPos, pageWidth - 30, 8, 'F');
    // Blue gradient header
    addGradientRect(15, yPos, pageWidth - 30, 8);
    // Table headers in white
    const headers = [
        'Aantal',
        'Beschrijving',
        'Uur',
        'Tarief',
        'BTW',
        'Totaal'
    ];
    const colWidths = [
        18,
        75,
        18,
        25,
        15,
        30
    ];
    let xPos = 15;
    headers.forEach((header, index)=>{
        addText(header, xPos + 2, yPos + 5, {
            color: [
                255,
                255,
                255
            ],
            style: 'bold',
            size: 9
        });
        xPos += colWidths[index];
    });
    // Table rows
    yPos += 8;
    factuurData.factuurregels.forEach((regel, index)=>{
        const rowY = yPos + index * 5;
        // Alternating row colors
        if (index % 2 === 1) {
            pdf.setFillColor(248, 249, 250);
            pdf.rect(15, rowY, pageWidth - 30, 5, 'F');
        }
        xPos = 15;
        const rowData = [
            regel.aantal.toString(),
            regel.beschrijving,
            regel.uren.toFixed(2),
            `€ ${regel.prijsPerUur.toFixed(2)}`,
            '21%',
            `€ ${regel.totaalExclBtw.toFixed(2)}`
        ];
        rowData.forEach((data, colIndex)=>{
            const align = colIndex === 1 ? 'left' : colIndex === 0 ? 'center' : 'right';
            const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 : align === 'center' ? xPos + colWidths[colIndex] / 2 : xPos + 2;
            if (colIndex === 1 && data.length > 30) {
                // Wrap long descriptions
                const lines = pdf.splitTextToSize(data, colWidths[colIndex] - 4);
                lines.forEach((line, lineIndex)=>{
                    addText(line, textX, rowY + 3 + lineIndex * 2.5, {
                        size: 8,
                        color: gray700
                    });
                });
            } else {
                addText(data, textX, rowY + 3, {
                    size: 8,
                    color: gray700,
                    align: align !== 'left' ? align : undefined
                });
            }
            xPos += colWidths[colIndex];
        });
    });
    // Extra Costs Section
    let extraCostsY = yPos + factuurData.factuurregels.length * 5 + 10;
    // Light blue background for extra costs
    pdf.setFillColor(...lightBlue);
    pdf.rect(15, extraCostsY, pageWidth - 30, 25, 'F');
    // Extra costs header
    addText('📧 Extra kosten', 18, extraCostsY + 6, {
        style: 'bold',
        size: 9,
        color: gray700
    });
    // Extra costs in 2x2 grid
    const extraCosts = [
        [
            '🚚 Voorrijkosten',
            factuurData.extraKosten.voorrijkosten || 25
        ],
        [
            '🅿️ Parkeerkosten',
            factuurData.extraKosten.parkeerkosten || 7.5
        ],
        [
            '⚡ Spoedservice',
            factuurData.extraKosten.spoedservice || 0
        ],
        [
            '🔧 Materiaalkosten',
            factuurData.extraKosten.materiaalkosten || 48.75
        ]
    ];
    extraCosts.forEach(([description, amount], index)=>{
        const row = Math.floor(index / 2);
        const col = index % 2;
        const x = 18 + col * 90;
        const y = extraCostsY + 12 + row * 8;
        // White background for each cost item
        pdf.setFillColor(255, 255, 255);
        pdf.rect(x, y - 2, 85, 6, 'F');
        addText(description, x + 2, y + 2, {
            size: 8,
            color: gray700
        });
        addText(`€ ${amount.toFixed(2)}`, x + 83, y + 2, {
            style: 'bold',
            size: 8,
            color: gray800,
            align: 'right'
        });
    });
    extraCostsY += 30;
    // Bottom section with 3 columns: Payment info, QR code, Totals
    const bottomY = extraCostsY + 10;
    // Column 1: Payment info
    const paymentX = 15;
    pdf.setFillColor(255, 255, 255);
    pdf.rect(paymentX, bottomY, 60, 40, 'F');
    addText('💳 Betalingsgegevens', paymentX + 2, bottomY + 6, {
        style: 'bold',
        size: 9,
        color: gray700
    });
    // IBAN info with blue background
    pdf.setFillColor(...lightBlue);
    pdf.rect(paymentX + 2, bottomY + 10, 56, 8, 'F');
    addText('🏦', paymentX + 4, bottomY + 15, {
        size: 8
    });
    addText('IBAN', paymentX + 12, bottomY + 13, {
        size: 7,
        color: gray600
    });
    addText(factuurData.bedrijfsInstellingen.ibanNummer || 'NL12 INGB 0000 1234', paymentX + 12, bottomY + 16, {
        style: 'bold',
        size: 8,
        color: gray900
    });
    addText(`t.n.v. ${factuurData.bedrijfsInstellingen.bedrijfsnaam}`, paymentX + 2, bottomY + 22, {
        size: 8,
        color: gray600
    });
    addText(`O.v.v. factuurnummer ${factuurData.factuurnummer}`, paymentX + 2, bottomY + 26, {
        size: 8,
        color: gray600
    });
    // iDEAL payment option
    pdf.setFillColor(...gray100);
    pdf.rect(paymentX + 2, bottomY + 30, 30, 6, 'F');
    addText('iDEAL betaling mogelijk', paymentX + 4, bottomY + 34, {
        size: 7,
        color: primaryBlue
    });
    // Column 2: QR Code
    const qrX = 85;
    const qrY = bottomY + 5;
    // QR Code background with gradient
    pdf.setFillColor(...lightBlue);
    pdf.rect(qrX, qrY, 40, 35, 'F');
    if (factuurData.betaalLink) {
        try {
            const qrCodeDataUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].toDataURL(factuurData.betaalLink, {
                width: 80,
                margin: 1,
                color: {
                    dark: '#2563eb',
                    light: '#ffffff'
                }
            });
            pdf.addImage(qrCodeDataUrl, 'PNG', qrX + 5, qrY + 5, 25, 25);
            addText('Scan voor betaling', qrX + 17.5, qrY + 35, {
                size: 8,
                color: primaryBlue,
                align: 'center'
            });
        } catch (error) {
            console.error('Error generating QR code:', error);
            // Fallback QR placeholder
            pdf.setFillColor(255, 255, 255);
            pdf.rect(qrX + 5, qrY + 5, 25, 25, 'F');
            addText('QR', qrX + 17.5, qrY + 18, {
                size: 12,
                style: 'bold',
                color: primaryBlue,
                align: 'center'
            });
            addText('Scan voor betaling', qrX + 17.5, qrY + 35, {
                size: 8,
                color: primaryBlue,
                align: 'center'
            });
        }
    }
    // Column 3: Totals
    const totalsX = 135;
    pdf.setFillColor(255, 255, 255);
    pdf.rect(totalsX, bottomY, 60, 40, 'F');
    addText('🧮 Totaaloverzicht', totalsX + 2, bottomY + 6, {
        style: 'bold',
        size: 9,
        color: gray700
    });
    const totals = [
        [
            'Subtotaal',
            `€ ${factuurData.subtotaal.toFixed(2)}`
        ],
        [
            'BTW 21%',
            `€ ${factuurData.btw21.toFixed(2)}`
        ],
        [
            'BTW 9%',
            `€ ${factuurData.btw9.toFixed(2)}`
        ],
        ...factuurData.kortingBedrag > 0 ? [
            [
                'Korting',
                `- € ${factuurData.kortingBedrag.toFixed(2)}`
            ]
        ] : []
    ];
    totals.forEach((total, index)=>{
        addText(total[0], totalsX + 2, bottomY + 12 + index * 4, {
            size: 8,
            color: gray600
        });
        addText(total[1], totalsX + 58, bottomY + 12 + index * 4, {
            size: 8,
            color: gray700,
            align: 'right'
        });
    });
    // Total amount with blue background
    addGradientRect(totalsX + 2, bottomY + 30, 56, 8);
    addText('Totaal incl. BTW', totalsX + 4, bottomY + 35, {
        color: [
            255,
            255,
            255
        ],
        style: 'bold',
        size: 9
    });
    addText(`€ ${factuurData.totaalInclBtw.toFixed(2)}`, totalsX + 56, bottomY + 35, {
        color: [
            255,
            255,
            255
        ],
        style: 'bold',
        size: 9,
        align: 'right'
    });
    // Footer
    const footerY = pageHeight - 30;
    // Light gray background for footer
    pdf.setFillColor(...gray50);
    pdf.rect(0, footerY, pageWidth, 30, 'F');
    // Footer content in 2 columns
    // Left column: Guarantee
    addText('🛡️ Garantie', 15, footerY + 6, {
        style: 'bold',
        size: 8,
        color: gray700
    });
    addText('Op alle uitgevoerde werkzaamheden geldt een garantieperiode van 3 maanden.', 15, footerY + 10, {
        size: 7,
        color: gray600
    });
    addText('Materiaal valt onder fabrieksgarantie.', 15, footerY + 13, {
        size: 7,
        color: gray600
    });
    // Right column: Terms
    addText('📄 Algemene voorwaarden', 110, footerY + 6, {
        style: 'bold',
        size: 8,
        color: gray700
    });
    addText('Deze factuur valt onder onze algemene voorwaarden zoals', 110, footerY + 10, {
        size: 7,
        color: gray600
    });
    addText('gepubliceerd op www.klusexperts.nl/algemene-voorwaarden.', 110, footerY + 13, {
        size: 7,
        color: gray600
    });
    // Bottom footer line
    const bottomFooterY = footerY + 20;
    // Page number
    addText('Pagina 1/1', 15, bottomFooterY, {
        size: 7,
        color: gray600
    });
    // Thank you message (center)
    addText('❤️ Bedankt voor uw vertrouwen in onze diensten', pageWidth / 2, bottomFooterY, {
        size: 7,
        color: gray700,
        align: 'center'
    });
    // Copyright (right)
    addText(`© 2025 ${factuurData.bedrijfsInstellingen.bedrijfsnaam}`, pageWidth - 15, bottomFooterY, {
        size: 7,
        color: gray600,
        align: 'right'
    });
    return pdf;
};
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__d4ee27._.js.map