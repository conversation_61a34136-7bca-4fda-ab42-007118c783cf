{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/utils/pdfGenerator.ts"], "sourcesContent": ["import { jsPDF } from 'jspdf';\nimport QRCode from 'qrcode';\n\ninterface FactuurData {\n  factuurnummer: string;\n  factuurdatum: Date;\n  vervaldatum: Date;\n  uitvoerdatum: Date;\n  klant: any;\n  factuurregels: any[];\n  extraKosten: any;\n  subtotaal: number;\n  kortingBedrag: number;\n  kortingPercentage: number;\n  btw21: number;\n  btw9: number;\n  totaalInclBtw: number;\n  betaalLink?: string;\n  bedrijfsInstellingen: any;\n}\n\nexport const generateProfessionalPDF = async (factuurData: FactuurData) => {\n  const pdf = new jsPDF('p', 'mm', 'a4');\n  const pageWidth = 210;\n  const pageHeight = 297;\n\n  // Colors matching the template\n  const primaryBlue = [37, 99, 235]; // #2563eb\n  const darkBlue = [30, 58, 138]; // #1e3a8a\n  const lightBlue = [240, 249, 255]; // #f0f9ff\n  const gray50 = [249, 250, 251]; // #f9fafb\n  const gray100 = [243, 244, 246]; // #f3f4f6\n  const gray600 = [75, 85, 99]; // #4b5563\n  const gray700 = [55, 65, 81]; // #374151\n  const gray800 = [31, 41, 55]; // #1f2937\n  const gray900 = [17, 24, 39]; // #111827\n\n  // Helper functions\n  const addText = (text: string, x: number, y: number, options: any = {}) => {\n    pdf.setFont(options.font || 'helvetica', options.style || 'normal');\n    pdf.setFontSize(options.size || 10);\n    if (options.color) {\n      pdf.setTextColor(...options.color);\n    } else {\n      pdf.setTextColor(0, 0, 0);\n    }\n    pdf.text(text, x, y, options.align ? { align: options.align } : undefined);\n  };\n\n  const addGradientRect = (x: number, y: number, width: number, height: number) => {\n    // Create gradient effect with multiple rectangles\n    const steps = 20;\n    for (let i = 0; i < steps; i++) {\n      const ratio = i / steps;\n      const r = primaryBlue[0] + (darkBlue[0] - primaryBlue[0]) * ratio;\n      const g = primaryBlue[1] + (darkBlue[1] - primaryBlue[1]) * ratio;\n      const b = primaryBlue[2] + (darkBlue[2] - primaryBlue[2]) * ratio;\n\n      pdf.setFillColor(r, g, b);\n      pdf.rect(x, y + (height * ratio), width, height / steps, 'F');\n    }\n  };\n\n  // Header with gradient background\n  addGradientRect(0, 0, pageWidth, 25);\n\n  // FACTUUR title in white with gradient effect\n  pdf.setTextColor(255, 255, 255);\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(18);\n  addText('FACTUUR', 15, 15, { color: [255, 255, 255], style: 'bold', size: 18 });\n\n  // Contact info in header\n  pdf.setFontSize(8);\n  const contactY = 20;\n  addText('📞 020-1234567', 15, contactY, { color: [255, 255, 255], size: 8 });\n  addText('✉ <EMAIL>', 70, contactY, { color: [255, 255, 255], size: 8 });\n  addText('🌐 www.klusexperts.nl', 140, contactY, { color: [255, 255, 255], size: 8 });\n\n  // Company logo area (right side)\n  if (factuurData.bedrijfsInstellingen.bedrijfsnaam) {\n    pdf.setFont('helvetica', 'bold');\n    pdf.setFontSize(12);\n    addText(factuurData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 15, {\n      color: [255, 255, 255],\n      style: 'bold',\n      size: 12,\n      align: 'right'\n    });\n  }\n\n  // Company and Client Info Section (3 columns)\n  let yPos = 35;\n\n  // Column 1: Klantgegevens\n  addText('Klantgegevens', 15, yPos, { style: 'bold', size: 10, color: gray800 });\n  yPos += 6;\n\n  const customerName = factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam\n    ? factuurData.klant.bedrijfsnaam\n    : `${factuurData.klant.voornaam} ${factuurData.klant.achternaam}`;\n\n  const customerDetails = [\n    customerName,\n    factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam\n      ? `t.a.v. ${factuurData.klant.voornaam} ${factuurData.klant.achternaam}`\n      : '',\n    `${factuurData.klant.adres} ${factuurData.klant.huisnummer}`,\n    `${factuurData.klant.postcode} ${factuurData.klant.stad}`,\n    factuurData.klant.kvkNummer ? `KVK: ${factuurData.klant.kvkNummer}` : '',\n    factuurData.klant.btwNummer ? `BTW: ${factuurData.klant.btwNummer}` : '',\n    factuurData.klant.telefoonnummer ? `Tel: ${factuurData.klant.telefoonnummer}` : ''\n  ].filter(Boolean);\n\n  customerDetails.forEach((detail, index) => {\n    const isBold = index === 0;\n    addText(detail, 15, yPos + (index * 4), {\n      style: isBold ? 'bold' : 'normal',\n      size: 9,\n      color: isBold ? gray900 : gray600\n    });\n  });\n\n  // Column 2: Bedrijfsgegevens\n  const col2X = 75;\n  yPos = 35;\n  addText('Bedrijfsgegevens', col2X, yPos, { style: 'bold', size: 10, color: gray800 });\n  yPos += 6;\n\n  const companyDetails = [\n    factuurData.bedrijfsInstellingen.bedrijfsnaam,\n    factuurData.bedrijfsInstellingen.contactpersoon ? `t.n.v. ${factuurData.bedrijfsInstellingen.contactpersoon}` : '',\n    factuurData.bedrijfsInstellingen.adres,\n    `${factuurData.bedrijfsInstellingen.postcode} ${factuurData.bedrijfsInstellingen.stad}`,\n    factuurData.bedrijfsInstellingen.kvkNummer ? `KVK: ${factuurData.bedrijfsInstellingen.kvkNummer}` : '',\n    factuurData.bedrijfsInstellingen.btwNummer ? `BTW: ${factuurData.bedrijfsInstellingen.btwNummer}` : '',\n    factuurData.bedrijfsInstellingen.ibanNummer ? `IBAN: ${factuurData.bedrijfsInstellingen.ibanNummer}` : ''\n  ].filter(Boolean);\n\n  companyDetails.forEach((detail, index) => {\n    const isBold = index === 0;\n    addText(detail, col2X, yPos + (index * 4), {\n      style: isBold ? 'bold' : 'normal',\n      size: 9,\n      color: isBold ? gray900 : gray600\n    });\n  });\n\n  // Column 3: Factuurdetails box (right side)\n  const boxX = 140;\n  const boxY = 35;\n  const boxWidth = 65;\n  const boxHeight = 45;\n\n  // Light gray background for invoice details\n  pdf.setFillColor(...gray50);\n  pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');\n\n  // Invoice details in 2x3 grid\n  const invoiceDetails = [\n    ['Factuurnummer', factuurData.factuurnummer],\n    ['Factuurdatum', factuurData.factuurdatum.toLocaleDateString('nl-NL')],\n    ['Vervaldatum', factuurData.vervaldatum.toLocaleDateString('nl-NL')],\n    ['Uitgevoerd op', factuurData.uitvoerdatum.toLocaleDateString('nl-NL')],\n    ['Service', 'Loodgieter'], // This could be dynamic based on service type\n    ['Betaaltermijn', '15 dagen']\n  ];\n\n  invoiceDetails.forEach((detail, index) => {\n    const row = Math.floor(index / 2);\n    const col = index % 2;\n    const x = boxX + 2 + (col * 30);\n    const y = boxY + 6 + (row * 12);\n\n    addText(detail[0], x, y, { size: 8, color: gray600 });\n    addText(detail[1], x, y + 4, { style: 'bold', size: 8, color: gray900 });\n  });\n\n  // Services Table\n  yPos = 90;\n\n  // Light blue background for table container\n  pdf.setFillColor(...lightBlue);\n  pdf.rect(15, yPos, pageWidth - 30, 8, 'F');\n\n  // Blue gradient header\n  addGradientRect(15, yPos, pageWidth - 30, 8);\n\n  // Table headers in white\n  const headers = ['Aantal', 'Beschrijving', 'Uur', 'Tarief', 'BTW', 'Totaal'];\n  const colWidths = [18, 75, 18, 25, 15, 30];\n  let xPos = 15;\n\n  headers.forEach((header, index) => {\n    addText(header, xPos + 2, yPos + 5, { color: [255, 255, 255], style: 'bold', size: 9 });\n    xPos += colWidths[index];\n  });\n\n  // Table rows\n  yPos += 8;\n  factuurData.factuurregels.forEach((regel, index) => {\n    const rowY = yPos + (index * 5);\n\n    // Alternating row colors\n    if (index % 2 === 1) {\n      pdf.setFillColor(248, 249, 250);\n      pdf.rect(15, rowY, pageWidth - 30, 5, 'F');\n    }\n\n    xPos = 15;\n    const rowData = [\n      regel.aantal.toString(),\n      regel.beschrijving,\n      regel.uren.toFixed(2),\n      `€ ${regel.prijsPerUur.toFixed(2)}`,\n      '21%',\n      `€ ${regel.totaalExclBtw.toFixed(2)}`\n    ];\n\n    rowData.forEach((data, colIndex) => {\n      const align = colIndex === 1 ? 'left' : colIndex === 0 ? 'center' : 'right';\n      const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 :\n                   align === 'center' ? xPos + colWidths[colIndex] / 2 : xPos + 2;\n\n      if (colIndex === 1 && data.length > 30) {\n        // Wrap long descriptions\n        const lines = pdf.splitTextToSize(data, colWidths[colIndex] - 4);\n        lines.forEach((line: string, lineIndex: number) => {\n          addText(line, textX, rowY + 3 + (lineIndex * 2.5), { size: 8, color: gray700 });\n        });\n      } else {\n        addText(data, textX, rowY + 3, {\n          size: 8,\n          color: gray700,\n          align: align !== 'left' ? align : undefined\n        });\n      }\n\n      xPos += colWidths[colIndex];\n    });\n  });\n\n  // Extra Costs Section\n  let extraCostsY = yPos + (factuurData.factuurregels.length * 5) + 10;\n\n  // Light blue background for extra costs\n  pdf.setFillColor(...lightBlue);\n  pdf.rect(15, extraCostsY, pageWidth - 30, 25, 'F');\n\n  // Extra costs header\n  addText('📧 Extra kosten', 18, extraCostsY + 6, { style: 'bold', size: 9, color: gray700 });\n\n  // Extra costs in 2x2 grid\n  const extraCosts = [\n    ['🚚 Voorrijkosten', factuurData.extraKosten.voorrijkosten || 25],\n    ['🅿️ Parkeerkosten', factuurData.extraKosten.parkeerkosten || 7.5],\n    ['⚡ Spoedservice', factuurData.extraKosten.spoedservice || 0],\n    ['🔧 Materiaalkosten', factuurData.extraKosten.materiaalkosten || 48.75]\n  ];\n\n  extraCosts.forEach(([description, amount], index) => {\n    const row = Math.floor(index / 2);\n    const col = index % 2;\n    const x = 18 + (col * 90);\n    const y = extraCostsY + 12 + (row * 8);\n\n    // White background for each cost item\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(x, y - 2, 85, 6, 'F');\n\n    addText(description, x + 2, y + 2, { size: 8, color: gray700 });\n    addText(`€ ${amount.toFixed(2)}`, x + 83, y + 2, { style: 'bold', size: 8, color: gray800, align: 'right' });\n  });\n\n  extraCostsY += 30;\n\n  // Bottom section with 3 columns: Payment info, QR code, Totals\n  const bottomY = extraCostsY + 10;\n\n  // Column 1: Payment info\n  const paymentX = 15;\n  pdf.setFillColor(255, 255, 255);\n  pdf.rect(paymentX, bottomY, 60, 40, 'F');\n\n  addText('💳 Betalingsgegevens', paymentX + 2, bottomY + 6, { style: 'bold', size: 9, color: gray700 });\n\n  // IBAN info with blue background\n  pdf.setFillColor(...lightBlue);\n  pdf.rect(paymentX + 2, bottomY + 10, 56, 8, 'F');\n\n  addText('🏦', paymentX + 4, bottomY + 15, { size: 8 });\n  addText('IBAN', paymentX + 12, bottomY + 13, { size: 7, color: gray600 });\n  addText(factuurData.bedrijfsInstellingen.ibanNummer || 'NL12 INGB 0000 1234', paymentX + 12, bottomY + 16, { style: 'bold', size: 8, color: gray900 });\n\n  addText(`t.n.v. ${factuurData.bedrijfsInstellingen.bedrijfsnaam}`, paymentX + 2, bottomY + 22, { size: 8, color: gray600 });\n  addText(`O.v.v. factuurnummer ${factuurData.factuurnummer}`, paymentX + 2, bottomY + 26, { size: 8, color: gray600 });\n\n  // iDEAL payment option\n  pdf.setFillColor(...gray100);\n  pdf.rect(paymentX + 2, bottomY + 30, 30, 6, 'F');\n  addText('iDEAL betaling mogelijk', paymentX + 4, bottomY + 34, { size: 7, color: primaryBlue });\n\n  // Column 2: QR Code\n  const qrX = 85;\n  const qrY = bottomY + 5;\n\n  // QR Code background with gradient\n  pdf.setFillColor(...lightBlue);\n  pdf.rect(qrX, qrY, 40, 35, 'F');\n\n  if (factuurData.betaalLink) {\n    try {\n      const qrCodeDataUrl = await QRCode.toDataURL(factuurData.betaalLink, {\n        width: 80,\n        margin: 1,\n        color: {\n          dark: '#2563eb',\n          light: '#ffffff'\n        }\n      });\n\n      pdf.addImage(qrCodeDataUrl, 'PNG', qrX + 5, qrY + 5, 25, 25);\n      addText('Scan voor betaling', qrX + 17.5, qrY + 35, { size: 8, color: primaryBlue, align: 'center' });\n    } catch (error) {\n      console.error('Error generating QR code:', error);\n      // Fallback QR placeholder\n      pdf.setFillColor(255, 255, 255);\n      pdf.rect(qrX + 5, qrY + 5, 25, 25, 'F');\n      addText('QR', qrX + 17.5, qrY + 18, { size: 12, style: 'bold', color: primaryBlue, align: 'center' });\n      addText('Scan voor betaling', qrX + 17.5, qrY + 35, { size: 8, color: primaryBlue, align: 'center' });\n    }\n  }\n\n  // Column 3: Totals\n  const totalsX = 135;\n  pdf.setFillColor(255, 255, 255);\n  pdf.rect(totalsX, bottomY, 60, 40, 'F');\n\n  addText('🧮 Totaaloverzicht', totalsX + 2, bottomY + 6, { style: 'bold', size: 9, color: gray700 });\n\n  const totals = [\n    ['Subtotaal', `€ ${factuurData.subtotaal.toFixed(2)}`],\n    ['BTW 21%', `€ ${factuurData.btw21.toFixed(2)}`],\n    ['BTW 9%', `€ ${factuurData.btw9.toFixed(2)}`],\n    ...(factuurData.kortingBedrag > 0 ? [['Korting', `- € ${factuurData.kortingBedrag.toFixed(2)}`]] : [])\n  ];\n\n  totals.forEach((total, index) => {\n    addText(total[0], totalsX + 2, bottomY + 12 + (index * 4), { size: 8, color: gray600 });\n    addText(total[1], totalsX + 58, bottomY + 12 + (index * 4), { size: 8, color: gray700, align: 'right' });\n  });\n\n  // Total amount with blue background\n  addGradientRect(totalsX + 2, bottomY + 30, 56, 8);\n  addText('Totaal incl. BTW', totalsX + 4, bottomY + 35, { color: [255, 255, 255], style: 'bold', size: 9 });\n  addText(`€ ${factuurData.totaalInclBtw.toFixed(2)}`, totalsX + 56, bottomY + 35, { color: [255, 255, 255], style: 'bold', size: 9, align: 'right' });\n\n  // Footer\n  const footerY = pageHeight - 30;\n\n  // Light gray background for footer\n  pdf.setFillColor(...gray50);\n  pdf.rect(0, footerY, pageWidth, 30, 'F');\n\n  // Footer content in 2 columns\n  // Left column: Guarantee\n  addText('🛡️ Garantie', 15, footerY + 6, { style: 'bold', size: 8, color: gray700 });\n  addText('Op alle uitgevoerde werkzaamheden geldt een garantieperiode van 3 maanden.', 15, footerY + 10, { size: 7, color: gray600 });\n  addText('Materiaal valt onder fabrieksgarantie.', 15, footerY + 13, { size: 7, color: gray600 });\n\n  // Right column: Terms\n  addText('📄 Algemene voorwaarden', 110, footerY + 6, { style: 'bold', size: 8, color: gray700 });\n  addText('Deze factuur valt onder onze algemene voorwaarden zoals', 110, footerY + 10, { size: 7, color: gray600 });\n  addText('gepubliceerd op www.klusexperts.nl/algemene-voorwaarden.', 110, footerY + 13, { size: 7, color: gray600 });\n\n  // Bottom footer line\n  const bottomFooterY = footerY + 20;\n\n  // Page number\n  addText('Pagina 1/1', 15, bottomFooterY, { size: 7, color: gray600 });\n\n  // Thank you message (center)\n  addText('❤️ Bedankt voor uw vertrouwen in onze diensten', pageWidth / 2, bottomFooterY, { size: 7, color: gray700, align: 'center' });\n\n  // Copyright (right)\n  addText(`© 2025 ${factuurData.bedrijfsInstellingen.bedrijfsnaam}`, pageWidth - 15, bottomFooterY, { size: 7, color: gray600, align: 'right' });\n\n  return pdf;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAoBO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,MAAM,IAAI,mJAAA,CAAA,QAAK,CAAC,KAAK,MAAM;IACjC,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,+BAA+B;IAC/B,MAAM,cAAc;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC7C,MAAM,WAAW;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC1C,MAAM,YAAY;QAAC;QAAK;QAAK;KAAI,EAAE,UAAU;IAC7C,MAAM,SAAS;QAAC;QAAK;QAAK;KAAI,EAAE,UAAU;IAC1C,MAAM,UAAU;QAAC;QAAK;QAAK;KAAI,EAAE,UAAU;IAC3C,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IAExC,mBAAmB;IACnB,MAAM,UAAU,CAAC,MAAc,GAAW,GAAW,UAAe,CAAC,CAAC;QACpE,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,aAAa,QAAQ,KAAK,IAAI;QAC1D,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI;QAChC,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,YAAY,IAAI,QAAQ,KAAK;QACnC,OAAO;YACL,IAAI,YAAY,CAAC,GAAG,GAAG;QACzB;QACA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,QAAQ,KAAK,GAAG;YAAE,OAAO,QAAQ,KAAK;QAAC,IAAI;IAClE;IAEA,MAAM,kBAAkB,CAAC,GAAW,GAAW,OAAe;QAC5D,kDAAkD;QAClD,MAAM,QAAQ;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,QAAQ,IAAI;YAClB,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAC5D,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAC5D,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAE5D,IAAI,YAAY,CAAC,GAAG,GAAG;YACvB,IAAI,IAAI,CAAC,GAAG,IAAK,SAAS,OAAQ,OAAO,SAAS,OAAO;QAC3D;IACF;IAEA,kCAAkC;IAClC,gBAAgB,GAAG,GAAG,WAAW;IAEjC,8CAA8C;IAC9C,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,QAAQ,WAAW,IAAI,IAAI;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,OAAO;QAAQ,MAAM;IAAG;IAE7E,yBAAyB;IACzB,IAAI,WAAW,CAAC;IAChB,MAAM,WAAW;IACjB,QAAQ,kBAAkB,IAAI,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IAC1E,QAAQ,yBAAyB,IAAI,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IACjF,QAAQ,yBAAyB,KAAK,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IAElF,iCAAiC;IACjC,IAAI,YAAY,oBAAoB,CAAC,YAAY,EAAE;QACjD,IAAI,OAAO,CAAC,aAAa;QACzB,IAAI,WAAW,CAAC;QAChB,QAAQ,YAAY,oBAAoB,CAAC,YAAY,EAAE,YAAY,IAAI,IAAI;YACzE,OAAO;gBAAC;gBAAK;gBAAK;aAAI;YACtB,OAAO;YACP,MAAM;YACN,OAAO;QACT;IACF;IAEA,8CAA8C;IAC9C,IAAI,OAAO;IAEX,0BAA0B;IAC1B,QAAQ,iBAAiB,IAAI,MAAM;QAAE,OAAO;QAAQ,MAAM;QAAI,OAAO;IAAQ;IAC7E,QAAQ;IAER,MAAM,eAAe,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa,YAAY,KAAK,CAAC,YAAY,GACvF,YAAY,KAAK,CAAC,YAAY,GAC9B,GAAG,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE;IAEnE,MAAM,kBAAkB;QACtB;QACA,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa,YAAY,KAAK,CAAC,YAAY,GAClE,CAAC,OAAO,EAAE,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE,GACtE;QACJ,GAAG,YAAY,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE;QAC5D,GAAG,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,IAAI,EAAE;QACzD,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,SAAS,EAAE,GAAG;QACtE,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,SAAS,EAAE,GAAG;QACtE,YAAY,KAAK,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,cAAc,EAAE,GAAG;KACjF,CAAC,MAAM,CAAC;IAET,gBAAgB,OAAO,CAAC,CAAC,QAAQ;QAC/B,MAAM,SAAS,UAAU;QACzB,QAAQ,QAAQ,IAAI,OAAQ,QAAQ,GAAI;YACtC,OAAO,SAAS,SAAS;YACzB,MAAM;YACN,OAAO,SAAS,UAAU;QAC5B;IACF;IAEA,6BAA6B;IAC7B,MAAM,QAAQ;IACd,OAAO;IACP,QAAQ,oBAAoB,OAAO,MAAM;QAAE,OAAO;QAAQ,MAAM;QAAI,OAAO;IAAQ;IACnF,QAAQ;IAER,MAAM,iBAAiB;QACrB,YAAY,oBAAoB,CAAC,YAAY;QAC7C,YAAY,oBAAoB,CAAC,cAAc,GAAG,CAAC,OAAO,EAAE,YAAY,oBAAoB,CAAC,cAAc,EAAE,GAAG;QAChH,YAAY,oBAAoB,CAAC,KAAK;QACtC,GAAG,YAAY,oBAAoB,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,oBAAoB,CAAC,IAAI,EAAE;QACvF,YAAY,oBAAoB,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,YAAY,oBAAoB,CAAC,SAAS,EAAE,GAAG;QACpG,YAAY,oBAAoB,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,YAAY,oBAAoB,CAAC,SAAS,EAAE,GAAG;QACpG,YAAY,oBAAoB,CAAC,UAAU,GAAG,CAAC,MAAM,EAAE,YAAY,oBAAoB,CAAC,UAAU,EAAE,GAAG;KACxG,CAAC,MAAM,CAAC;IAET,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,MAAM,SAAS,UAAU;QACzB,QAAQ,QAAQ,OAAO,OAAQ,QAAQ,GAAI;YACzC,OAAO,SAAS,SAAS;YACzB,MAAM;YACN,OAAO,SAAS,UAAU;QAC5B;IACF;IAEA,4CAA4C;IAC5C,MAAM,OAAO;IACb,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,YAAY;IAElB,4CAA4C;IAC5C,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,MAAM,MAAM,UAAU,WAAW;IAE1C,8BAA8B;IAC9B,MAAM,iBAAiB;QACrB;YAAC;YAAiB,YAAY,aAAa;SAAC;QAC5C;YAAC;YAAgB,YAAY,YAAY,CAAC,kBAAkB,CAAC;SAAS;QACtE;YAAC;YAAe,YAAY,WAAW,CAAC,kBAAkB,CAAC;SAAS;QACpE;YAAC;YAAiB,YAAY,YAAY,CAAC,kBAAkB,CAAC;SAAS;QACvE;YAAC;YAAW;SAAa;QACzB;YAAC;YAAiB;SAAW;KAC9B;IAED,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;QAC/B,MAAM,MAAM,QAAQ;QACpB,MAAM,IAAI,OAAO,IAAK,MAAM;QAC5B,MAAM,IAAI,OAAO,IAAK,MAAM;QAE5B,QAAQ,MAAM,CAAC,EAAE,EAAE,GAAG,GAAG;YAAE,MAAM;YAAG,OAAO;QAAQ;QACnD,QAAQ,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI,GAAG;YAAE,OAAO;YAAQ,MAAM;YAAG,OAAO;QAAQ;IACxE;IAEA,iBAAiB;IACjB,OAAO;IAEP,4CAA4C;IAC5C,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;IAEtC,uBAAuB;IACvB,gBAAgB,IAAI,MAAM,YAAY,IAAI;IAE1C,yBAAyB;IACzB,MAAM,UAAU;QAAC;QAAU;QAAgB;QAAO;QAAU;QAAO;KAAS;IAC5E,MAAM,YAAY;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAC1C,IAAI,OAAO;IAEX,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACvB,QAAQ,QAAQ,OAAO,GAAG,OAAO,GAAG;YAAE,OAAO;gBAAC;gBAAK;gBAAK;aAAI;YAAE,OAAO;YAAQ,MAAM;QAAE;QACrF,QAAQ,SAAS,CAAC,MAAM;IAC1B;IAEA,aAAa;IACb,QAAQ;IACR,YAAY,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO;QACxC,MAAM,OAAO,OAAQ,QAAQ;QAE7B,yBAAyB;QACzB,IAAI,QAAQ,MAAM,GAAG;YACnB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;QACxC;QAEA,OAAO;QACP,MAAM,UAAU;YACd,MAAM,MAAM,CAAC,QAAQ;YACrB,MAAM,YAAY;YAClB,MAAM,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,EAAE,EAAE,MAAM,WAAW,CAAC,OAAO,CAAC,IAAI;YACnC;YACA,CAAC,EAAE,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;SACtC;QAED,QAAQ,OAAO,CAAC,CAAC,MAAM;YACrB,MAAM,QAAQ,aAAa,IAAI,SAAS,aAAa,IAAI,WAAW;YACpE,MAAM,QAAQ,UAAU,UAAU,OAAO,SAAS,CAAC,SAAS,GAAG,IAClD,UAAU,WAAW,OAAO,SAAS,CAAC,SAAS,GAAG,IAAI,OAAO;YAE1E,IAAI,aAAa,KAAK,KAAK,MAAM,GAAG,IAAI;gBACtC,yBAAyB;gBACzB,MAAM,QAAQ,IAAI,eAAe,CAAC,MAAM,SAAS,CAAC,SAAS,GAAG;gBAC9D,MAAM,OAAO,CAAC,CAAC,MAAc;oBAC3B,QAAQ,MAAM,OAAO,OAAO,IAAK,YAAY,KAAM;wBAAE,MAAM;wBAAG,OAAO;oBAAQ;gBAC/E;YACF,OAAO;gBACL,QAAQ,MAAM,OAAO,OAAO,GAAG;oBAC7B,MAAM;oBACN,OAAO;oBACP,OAAO,UAAU,SAAS,QAAQ;gBACpC;YACF;YAEA,QAAQ,SAAS,CAAC,SAAS;QAC7B;IACF;IAEA,sBAAsB;IACtB,IAAI,cAAc,OAAQ,YAAY,aAAa,CAAC,MAAM,GAAG,IAAK;IAElE,wCAAwC;IACxC,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,IAAI,aAAa,YAAY,IAAI,IAAI;IAE9C,qBAAqB;IACrB,QAAQ,mBAAmB,IAAI,cAAc,GAAG;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAEzF,0BAA0B;IAC1B,MAAM,aAAa;QACjB;YAAC;YAAoB,YAAY,WAAW,CAAC,aAAa,IAAI;SAAG;QACjE;YAAC;YAAqB,YAAY,WAAW,CAAC,aAAa,IAAI;SAAI;QACnE;YAAC;YAAkB,YAAY,WAAW,CAAC,YAAY,IAAI;SAAE;QAC7D;YAAC;YAAsB,YAAY,WAAW,CAAC,eAAe,IAAI;SAAM;KACzE;IAED,WAAW,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,EAAE;QACzC,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;QAC/B,MAAM,MAAM,QAAQ;QACpB,MAAM,IAAI,KAAM,MAAM;QACtB,MAAM,IAAI,cAAc,KAAM,MAAM;QAEpC,sCAAsC;QACtC,IAAI,YAAY,CAAC,KAAK,KAAK;QAC3B,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG;QAE1B,QAAQ,aAAa,IAAI,GAAG,IAAI,GAAG;YAAE,MAAM;YAAG,OAAO;QAAQ;QAC7D,QAAQ,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG;YAAE,OAAO;YAAQ,MAAM;YAAG,OAAO;YAAS,OAAO;QAAQ;IAC5G;IAEA,eAAe;IAEf,+DAA+D;IAC/D,MAAM,UAAU,cAAc;IAE9B,yBAAyB;IACzB,MAAM,WAAW;IACjB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,IAAI,CAAC,UAAU,SAAS,IAAI,IAAI;IAEpC,QAAQ,wBAAwB,WAAW,GAAG,UAAU,GAAG;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAEpG,iCAAiC;IACjC,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,GAAG;IAE5C,QAAQ,MAAM,WAAW,GAAG,UAAU,IAAI;QAAE,MAAM;IAAE;IACpD,QAAQ,QAAQ,WAAW,IAAI,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IACvE,QAAQ,YAAY,oBAAoB,CAAC,UAAU,IAAI,uBAAuB,WAAW,IAAI,UAAU,IAAI;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAEpJ,QAAQ,CAAC,OAAO,EAAE,YAAY,oBAAoB,CAAC,YAAY,EAAE,EAAE,WAAW,GAAG,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IACzH,QAAQ,CAAC,qBAAqB,EAAE,YAAY,aAAa,EAAE,EAAE,WAAW,GAAG,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IAEnH,uBAAuB;IACvB,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,GAAG;IAC5C,QAAQ,2BAA2B,WAAW,GAAG,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAY;IAE7F,oBAAoB;IACpB,MAAM,MAAM;IACZ,MAAM,MAAM,UAAU;IAEtB,mCAAmC;IACnC,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI;IAE3B,IAAI,YAAY,UAAU,EAAE;QAC1B,IAAI;YACF,MAAM,gBAAgB,MAAM,sIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,YAAY,UAAU,EAAE;gBACnE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBACL,MAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,QAAQ,CAAC,eAAe,OAAO,MAAM,GAAG,MAAM,GAAG,IAAI;YACzD,QAAQ,sBAAsB,MAAM,MAAM,MAAM,IAAI;gBAAE,MAAM;gBAAG,OAAO;gBAAa,OAAO;YAAS;QACrG,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,0BAA0B;YAC1B,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,IAAI;YACnC,QAAQ,MAAM,MAAM,MAAM,MAAM,IAAI;gBAAE,MAAM;gBAAI,OAAO;gBAAQ,OAAO;gBAAa,OAAO;YAAS;YACnG,QAAQ,sBAAsB,MAAM,MAAM,MAAM,IAAI;gBAAE,MAAM;gBAAG,OAAO;gBAAa,OAAO;YAAS;QACrG;IACF;IAEA,mBAAmB;IACnB,MAAM,UAAU;IAChB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,IAAI,CAAC,SAAS,SAAS,IAAI,IAAI;IAEnC,QAAQ,sBAAsB,UAAU,GAAG,UAAU,GAAG;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAEjG,MAAM,SAAS;QACb;YAAC;YAAa,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,OAAO,CAAC,IAAI;SAAC;QACtD;YAAC;YAAW,CAAC,EAAE,EAAE,YAAY,KAAK,CAAC,OAAO,CAAC,IAAI;SAAC;QAChD;YAAC;YAAU,CAAC,EAAE,EAAE,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI;SAAC;WAC1C,YAAY,aAAa,GAAG,IAAI;YAAC;gBAAC;gBAAW,CAAC,IAAI,EAAE,YAAY,aAAa,CAAC,OAAO,CAAC,IAAI;aAAC;SAAC,GAAG,EAAE;KACtG;IAED,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,QAAQ,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,UAAU,KAAM,QAAQ,GAAI;YAAE,MAAM;YAAG,OAAO;QAAQ;QACrF,QAAQ,KAAK,CAAC,EAAE,EAAE,UAAU,IAAI,UAAU,KAAM,QAAQ,GAAI;YAAE,MAAM;YAAG,OAAO;YAAS,OAAO;QAAQ;IACxG;IAEA,oCAAoC;IACpC,gBAAgB,UAAU,GAAG,UAAU,IAAI,IAAI;IAC/C,QAAQ,oBAAoB,UAAU,GAAG,UAAU,IAAI;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,OAAO;QAAQ,MAAM;IAAE;IACxG,QAAQ,CAAC,EAAE,EAAE,YAAY,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAElJ,SAAS;IACT,MAAM,UAAU,aAAa;IAE7B,mCAAmC;IACnC,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,GAAG,SAAS,WAAW,IAAI;IAEpC,8BAA8B;IAC9B,yBAAyB;IACzB,QAAQ,gBAAgB,IAAI,UAAU,GAAG;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAClF,QAAQ,8EAA8E,IAAI,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IAClI,QAAQ,0CAA0C,IAAI,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IAE9F,sBAAsB;IACtB,QAAQ,2BAA2B,KAAK,UAAU,GAAG;QAAE,OAAO;QAAQ,MAAM;QAAG,OAAO;IAAQ;IAC9F,QAAQ,2DAA2D,KAAK,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IAChH,QAAQ,4DAA4D,KAAK,UAAU,IAAI;QAAE,MAAM;QAAG,OAAO;IAAQ;IAEjH,qBAAqB;IACrB,MAAM,gBAAgB,UAAU;IAEhC,cAAc;IACd,QAAQ,cAAc,IAAI,eAAe;QAAE,MAAM;QAAG,OAAO;IAAQ;IAEnE,6BAA6B;IAC7B,QAAQ,kDAAkD,YAAY,GAAG,eAAe;QAAE,MAAM;QAAG,OAAO;QAAS,OAAO;IAAS;IAEnI,oBAAoB;IACpB,QAAQ,CAAC,OAAO,EAAE,YAAY,oBAAoB,CAAC,YAAY,EAAE,EAAE,YAAY,IAAI,eAAe;QAAE,MAAM;QAAG,OAAO;QAAS,OAAO;IAAQ;IAE5I,OAAO;AACT"}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}