{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/tijdregistratie/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { \n  Clock, \n  Play, \n  Pause, \n  Square, \n  RotateCcw, \n  Plus, \n  Users,\n  Calculator,\n  ListTodo,\n  Timer\n} from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function TijdregistratiePage() {\n  const { \n    klanten, \n    tijdregistraties, \n    getKlant,\n    startTijdregistratie,\n    stopTijdregistratie,\n    pauseTijdregistratie,\n    resumeTijdregistratie,\n    resetTijdregistratie,\n    addTijdToRegistratie,\n    addToWachtlijst\n  } = useApp();\n\n  const [selectedKlantId, setSelectedKlantId] = useState('');\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [extraUren, setExtraUren] = useState(0);\n  const [extraMinuten, setExtraMinuten] = useState(0);\n\n  // Update current time every second\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Get active tijdregistraties\n  const activeTijdregistraties = tijdregistraties.filter(t => t.status === 'actief' || t.status === 'gepauzeerd');\n\n  const formatTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getCurrentTime = (tijdregistratie: any) => {\n    if (tijdregistratie.status === 'actief') {\n      const elapsed = Math.floor((currentTime.getTime() - new Date(tijdregistratie.startTijd).getTime()) / 1000);\n      return elapsed + tijdregistratie.totaalTijd;\n    }\n    return tijdregistratie.totaalTijd;\n  };\n\n  const handleStartTijdregistratie = () => {\n    if (!selectedKlantId) {\n      alert('Selecteer eerst een klant');\n      return;\n    }\n    startTijdregistratie(selectedKlantId);\n    setSelectedKlantId('');\n  };\n\n  const handleAddExtraTijd = (tijdregistratieId: string) => {\n    if (extraUren > 0 || extraMinuten > 0) {\n      addTijdToRegistratie(tijdregistratieId, extraUren, extraMinuten);\n      setExtraUren(0);\n      setExtraMinuten(0);\n    }\n  };\n\n  const handleAddToWachtlijst = (tijdregistratie: any) => {\n    const klant = getKlant(tijdregistratie.klantId);\n    if (klant) {\n      addToWachtlijst(tijdregistratie.klantId, tijdregistratie.id, `Wachtlijst voor ${klant.voornaam} ${klant.achternaam}`);\n      pauseTijdregistratie(tijdregistratie.id);\n    }\n  };\n\n  const handleCreateFactuur = (tijdregistratie: any) => {\n    // Navigate to factuur creation with tijdregistratie data\n    const params = new URLSearchParams({\n      klantId: tijdregistratie.klantId,\n      tijdregistratieId: tijdregistratie.id,\n      uren: (getCurrentTime(tijdregistratie) / 3600).toFixed(2)\n    });\n    window.location.href = `/facturen/nieuw?${params.toString()}`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Tijdregistratie</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Registreer uw werktijd per klant\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2 mt-4 sm:mt-0\">\n          <div className=\"text-sm text-muted-foreground\">\n            {currentTime.toLocaleTimeString('nl-NL')}\n          </div>\n        </div>\n      </div>\n\n      {/* Start New Registration */}\n      <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n        <h2 className=\"text-lg font-semibold text-foreground mb-4\">Nieuwe Tijdregistratie Starten</h2>\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <select\n              value={selectedKlantId}\n              onChange={(e) => setSelectedKlantId(e.target.value)}\n              className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n            >\n              <option value=\"\">Selecteer een klant...</option>\n              {klanten.map((klant) => (\n                <option key={klant.id} value={klant.id}>\n                  {klant.type === 'bedrijf' && klant.bedrijfsnaam \n                    ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`\n                    : `${klant.voornaam} ${klant.achternaam}`\n                  }\n                </option>\n              ))}\n            </select>\n          </div>\n          <button\n            onClick={handleStartTijdregistratie}\n            disabled={!selectedKlantId}\n            className=\"flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Play className=\"h-4 w-4\" />\n            <span>Start Tijdregistratie</span>\n          </button>\n        </div>\n        \n        {klanten.length === 0 && (\n          <div className=\"mt-4 p-4 bg-muted rounded-lg\">\n            <p className=\"text-muted-foreground text-sm\">\n              Geen klanten beschikbaar. \n              <Link href=\"/klanten/nieuw\" className=\"text-primary hover:underline ml-1\">\n                Voeg eerst een klant toe\n              </Link>\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Active Registrations */}\n      {activeTijdregistraties.length > 0 && (\n        <div className=\"space-y-4\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Actieve Tijdregistraties</h2>\n          \n          {activeTijdregistraties.map((tijdregistratie) => {\n            const klant = getKlant(tijdregistratie.klantId);\n            const currentSeconds = getCurrentTime(tijdregistratie);\n            \n            return (\n              <div key={tijdregistratie.id} className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n                <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n                  {/* Klant Info */}\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n                      {klant ? (\n                        klant.type === 'bedrijf' && klant.bedrijfsnaam \n                          ? `${klant.bedrijfsnaam}`\n                          : `${klant.voornaam} ${klant.achternaam}`\n                      ) : 'Onbekende klant'}\n                    </h3>\n                    {klant && (\n                      <div className=\"text-sm text-muted-foreground space-y-1\">\n                        {klant.type === 'bedrijf' && klant.bedrijfsnaam && (\n                          <p>Contactpersoon: {klant.voornaam} {klant.achternaam}</p>\n                        )}\n                        <p>{klant.adres} {klant.huisnummer}, {klant.postcode} {klant.stad}</p>\n                        <p>{klant.telefoonnummer} • {klant.email}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Stopwatch */}\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl font-mono font-bold text-foreground mb-2\">\n                      {formatTime(currentSeconds)}\n                    </div>\n                    <div className={`text-sm font-medium ${\n                      tijdregistratie.status === 'actief' ? 'text-green-600' : 'text-orange-600'\n                    }`}>\n                      {tijdregistratie.status === 'actief' ? 'Actief' : 'Gepauzeerd'}\n                    </div>\n                  </div>\n\n                  {/* Controls */}\n                  <div className=\"flex flex-col space-y-2 lg:ml-6\">\n                    <div className=\"flex items-center space-x-2\">\n                      {tijdregistratie.status === 'actief' ? (\n                        <button\n                          onClick={() => pauseTijdregistratie(tijdregistratie.id)}\n                          className=\"flex items-center space-x-1 bg-orange-500 text-white px-3 py-2 rounded-lg hover:bg-orange-600 transition-colors\"\n                        >\n                          <Pause className=\"h-4 w-4\" />\n                          <span>Pauze</span>\n                        </button>\n                      ) : (\n                        <button\n                          onClick={() => resumeTijdregistratie(tijdregistratie.id)}\n                          className=\"flex items-center space-x-1 bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors\"\n                        >\n                          <Play className=\"h-4 w-4\" />\n                          <span>Hervatten</span>\n                        </button>\n                      )}\n                      \n                      <button\n                        onClick={() => stopTijdregistratie(tijdregistratie.id)}\n                        className=\"flex items-center space-x-1 bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors\"\n                      >\n                        <Square className=\"h-4 w-4\" />\n                        <span>Stop</span>\n                      </button>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => resetTijdregistratie(tijdregistratie.id)}\n                        className=\"flex items-center space-x-1 bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors\"\n                      >\n                        <RotateCcw className=\"h-4 w-4\" />\n                        <span>Reset</span>\n                      </button>\n                      \n                      <button\n                        onClick={() => handleAddToWachtlijst(tijdregistratie)}\n                        className=\"flex items-center space-x-1 bg-purple-500 text-white px-3 py-2 rounded-lg hover:bg-purple-600 transition-colors\"\n                      >\n                        <ListTodo className=\"h-4 w-4\" />\n                        <span>Wachtlijst</span>\n                      </button>\n                    </div>\n\n                    {/* Extra tijd toevoegen */}\n                    <div className=\"flex items-center space-x-2 pt-2 border-t border-border\">\n                      <div className=\"flex items-center space-x-1\">\n                        <input\n                          type=\"number\"\n                          min=\"0\"\n                          max=\"23\"\n                          value={extraUren}\n                          onChange={(e) => setExtraUren(parseInt(e.target.value) || 0)}\n                          className=\"w-16 px-2 py-1 text-sm border border-input rounded bg-background text-foreground\"\n                          placeholder=\"0\"\n                        />\n                        <span className=\"text-xs text-muted-foreground\">u</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <input\n                          type=\"number\"\n                          min=\"0\"\n                          max=\"59\"\n                          value={extraMinuten}\n                          onChange={(e) => setExtraMinuten(parseInt(e.target.value) || 0)}\n                          className=\"w-16 px-2 py-1 text-sm border border-input rounded bg-background text-foreground\"\n                          placeholder=\"0\"\n                        />\n                        <span className=\"text-xs text-muted-foreground\">m</span>\n                      </div>\n                      <button\n                        onClick={() => handleAddExtraTijd(tijdregistratie.id)}\n                        className=\"flex items-center space-x-1 bg-blue-500 text-white px-2 py-1 rounded text-sm hover:bg-blue-600 transition-colors\"\n                      >\n                        <Plus className=\"h-3 w-3\" />\n                        <span>Tijd</span>\n                      </button>\n                    </div>\n\n                    {/* Factuur maken */}\n                    {tijdregistratie.status === 'gestopt' && (\n                      <button\n                        onClick={() => handleCreateFactuur(tijdregistratie)}\n                        className=\"flex items-center space-x-1 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors w-full justify-center\"\n                      >\n                        <Calculator className=\"h-4 w-4\" />\n                        <span>Factuur Maken</span>\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* No Active Registrations */}\n      {activeTijdregistraties.length === 0 && (\n        <div className=\"bg-card border border-border rounded-lg p-12 text-center shadow-sm\">\n          <Timer className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n            Geen actieve tijdregistraties\n          </h3>\n          <p className=\"text-muted-foreground mb-6\">\n            Start een nieuwe tijdregistratie door een klant te selecteren en op \"Start Tijdregistratie\" te klikken.\n          </p>\n          {klanten.length === 0 && (\n            <Link\n              href=\"/klanten/nieuw\"\n              className=\"inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              <Users className=\"h-4 w-4\" />\n              <span>Eerste Klant Toevoegen</span>\n            </Link>\n          )}\n        </div>\n      )}\n\n      {/* Quick Actions */}\n      <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-4\">Snelle Acties</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n          <Link\n            href=\"/wachtlijst\"\n            className=\"flex items-center justify-center p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\n          >\n            <ListTodo className=\"h-5 w-5 mr-2\" />\n            Bekijk Wachtlijst\n          </Link>\n          <Link\n            href=\"/snelle-berekening\"\n            className=\"flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n          >\n            <Calculator className=\"h-5 w-5 mr-2\" />\n            Snelle Berekening\n          </Link>\n          <Link\n            href=\"/klanten\"\n            className=\"flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          >\n            <Users className=\"h-5 w-5 mr-2\" />\n            Klanten Beheren\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAaA;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;AAkBe,SAAS;IACtB,MAAM,EACJ,OAAO,EACP,gBAAgB,EAChB,QAAQ,EACR,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,eAAe,EAChB,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAET,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,yBAAyB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,EAAE,MAAM,KAAK;IAElG,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC1H;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,gBAAgB,MAAM,KAAK,UAAU;YACvC,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,YAAY,OAAO,KAAK,IAAI,KAAK,gBAAgB,SAAS,EAAE,OAAO,EAAE,IAAI;YACrG,OAAO,UAAU,gBAAgB,UAAU;QAC7C;QACA,OAAO,gBAAgB,UAAU;IACnC;IAEA,MAAM,6BAA6B;QACjC,IAAI,CAAC,iBAAiB;YACpB,MAAM;YACN;QACF;QACA,qBAAqB;QACrB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY,KAAK,eAAe,GAAG;YACrC,qBAAqB,mBAAmB,WAAW;YACnD,aAAa;YACb,gBAAgB;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,SAAS,gBAAgB,OAAO;QAC9C,IAAI,OAAO;YACT,gBAAgB,gBAAgB,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE;YACpH,qBAAqB,gBAAgB,EAAE;QACzC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,yDAAyD;QACzD,MAAM,SAAS,IAAI,gBAAgB;YACjC,SAAS,gBAAgB,OAAO;YAChC,mBAAmB,gBAAgB,EAAE;YACrC,MAAM,CAAC,eAAe,mBAAmB,IAAI,EAAE,OAAO,CAAC;QACzD;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,gBAAgB,EAAE,OAAO,QAAQ,IAAI;IAC/D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,YAAY,kBAAkB,CAAC;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,QAAQ,GAAG,CAAC,CAAC,sBACZ,8OAAC;gDAAsB,OAAO,MAAM,EAAE;0DACnC,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC3C,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,GAC/D,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE;+CAHhC,MAAM,EAAE;;;;;;;;;;;;;;;;0CAS3B,8OAAC;gCACC,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAIT,QAAQ,MAAM,KAAK,mBAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAgC;8CAE3C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAiB,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;YASjF,uBAAuB,MAAM,GAAG,mBAC/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAErD,uBAAuB,GAAG,CAAC,CAAC;wBAC3B,MAAM,QAAQ,SAAS,gBAAgB,OAAO;wBAC9C,MAAM,iBAAiB,eAAe;wBAEtC,qBACE,8OAAC;4BAA6B,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,QACC,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC1C,GAAG,MAAM,YAAY,EAAE,GACvB,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE,GACzC;;;;;;4CAEL,uBACC,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,kBAC7C,8OAAC;;4DAAE;4DAAiB,MAAM,QAAQ;4DAAC;4DAAE,MAAM,UAAU;;;;;;;kEAEvD,8OAAC;;4DAAG,MAAM,KAAK;4DAAC;4DAAE,MAAM,UAAU;4DAAC;4DAAG,MAAM,QAAQ;4DAAC;4DAAE,MAAM,IAAI;;;;;;;kEACjE,8OAAC;;4DAAG,MAAM,cAAc;4DAAC;4DAAI,MAAM,KAAK;;;;;;;;;;;;;;;;;;;kDAM9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW;;;;;;0DAEd,8OAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,gBAAgB,MAAM,KAAK,WAAW,mBAAmB,mBACzD;0DACC,gBAAgB,MAAM,KAAK,WAAW,WAAW;;;;;;;;;;;;kDAKtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,gBAAgB,MAAM,KAAK,yBAC1B,8OAAC;wDACC,SAAS,IAAM,qBAAqB,gBAAgB,EAAE;wDACtD,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;6EAGR,8OAAC;wDACC,SAAS,IAAM,sBAAsB,gBAAgB,EAAE;wDACvD,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;kEAIV,8OAAC;wDACC,SAAS,IAAM,oBAAoB,gBAAgB,EAAE;wDACrD,WAAU;;0EAEV,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,qBAAqB,gBAAgB,EAAE;wDACtD,WAAU;;0EAEV,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;;;;;;kEAGR,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;;0EAEV,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC1D,WAAU;gEACV,aAAY;;;;;;0EAEd,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;kEAElD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC7D,WAAU;gEACV,aAAY;;;;;;0EAEd,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;kEAElD,8OAAC;wDACC,SAAS,IAAM,mBAAmB,gBAAgB,EAAE;wDACpD,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;4CAKT,gBAAgB,MAAM,KAAK,2BAC1B,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;;kEAEV,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;2BA5HN,gBAAgB,EAAE;;;;;oBAmIhC;;;;;;;YAKH,uBAAuB,MAAM,KAAK,mBACjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAG3D,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;oBAGzC,QAAQ,MAAM,KAAK,mBAClB,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C"}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "file": "pause.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/pause.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '14', y: '4', width: '4', height: '16', rx: '1', key: 'zuxfzm' }],\n  ['rect', { x: '6', y: '4', width: '4', height: '16', rx: '1', key: '1okwgv' }],\n];\n\n/**\n * @component @name Pause\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNCIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iMTYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjYiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjE2IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pause\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pause = createLucideIcon('pause', __iconNode);\n\nexport default Pause;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC/E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "file": "square.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n];\n\n/**\n * @component @name Square\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Square = createLucideIcon('square', __iconNode);\n\nexport default Square;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "file": "rotate-ccw.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/rotate-ccw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8', key: '1357e3' }],\n  ['path', { d: 'M3 3v5h5', key: '1xhq8a' }],\n];\n\n/**\n * @component @name RotateCcw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAxIDAgOS05IDkuNzUgOS43NSAwIDAgMC02Ljc0IDIuNzRMMyA4IiAvPgogIDxwYXRoIGQ9Ik0zIDN2NWg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-ccw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCcw = createLucideIcon('rotate-ccw', __iconNode);\n\nexport default RotateCcw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "file": "timer.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/timer.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '10', x2: '14', y1: '2', y2: '2', key: '14vaq8' }],\n  ['line', { x1: '12', x2: '15', y1: '14', y2: '11', key: '17fdiu' }],\n  ['circle', { cx: '12', cy: '14', r: '8', key: '1e1u0o' }],\n];\n\n/**\n * @component @name Timer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTAiIHgyPSIxNCIgeTE9IjIiIHkyPSIyIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjE1IiB5MT0iMTQiIHkyPSIxMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE0IiByPSI4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/timer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Timer = createLucideIcon('timer', __iconNode);\n\nexport default Timer;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}