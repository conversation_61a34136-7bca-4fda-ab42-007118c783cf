{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/facturen/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport { useApp } from '@/contexts/AppContext';\nimport {\n  ArrowLeft,\n  Download,\n  Share2,\n  Edit,\n  Send,\n  Eye,\n  FileText,\n  Building,\n  User,\n  Calendar,\n  Euro\n} from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function FactuurDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const { getFactuur, getKlant, bedrijfsInstellingen } = useApp();\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n\n  const factuurId = params.id as string;\n  const factuur = getFactuur(factuurId);\n  const klant = factuur ? getKlant(factuur.klantId) : null;\n\n  if (!factuur) {\n    return (\n      <div className=\"text-center py-12\">\n        <FileText className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n        <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n          Factuur niet gevonden\n        </h3>\n        <p className=\"text-muted-foreground mb-6\">\n          De opgevraagde factuur bestaat niet of is verwijderd.\n        </p>\n        <Link\n          href=\"/facturen\"\n          className=\"inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\"\n        >\n          <ArrowLeft className=\"h-4 w-4\" />\n          <span>Terug naar Facturen</span>\n        </Link>\n      </div>\n    );\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'concept': return 'bg-gray-100 text-gray-800';\n      case 'verzonden': return 'bg-blue-100 text-blue-800';\n      case 'betaald': return 'bg-green-100 text-green-800';\n      case 'vervallen': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusLabel = (status: string) => {\n    switch (status) {\n      case 'concept': return 'Concept';\n      case 'verzonden': return 'Verzonden';\n      case 'betaald': return 'Betaald';\n      case 'vervallen': return 'Vervallen';\n      default: return status;\n    }\n  };\n\n  const handleGeneratePDF = async () => {\n    setIsGeneratingPDF(true);\n    try {\n      // Import jsPDF dynamically\n      const { jsPDF } = await import('jspdf');\n      const html2canvas = (await import('html2canvas')).default;\n\n      // Get the factuur content element\n      const element = document.getElementById('factuur-content');\n      if (!element) return;\n\n      // Generate canvas from HTML\n      const canvas = await html2canvas(element, {\n        scale: 2,\n        useCORS: true,\n        allowTaint: true,\n      });\n\n      // Create PDF\n      const pdf = new jsPDF('p', 'mm', 'a4');\n      const imgData = canvas.toDataURL('image/png');\n\n      const imgWidth = 210; // A4 width in mm\n      const pageHeight = 295; // A4 height in mm\n      const imgHeight = (canvas.height * imgWidth) / canvas.width;\n      let heightLeft = imgHeight;\n\n      let position = 0;\n\n      // Add first page\n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n      heightLeft -= pageHeight;\n\n      // Add additional pages if needed\n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight;\n        pdf.addPage();\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n      }\n\n      // Save PDF\n      pdf.save(`Factuur-${factuur.factuurnummer}.pdf`);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Er is een fout opgetreden bij het genereren van de PDF');\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  const handleShare = (method: 'whatsapp' | 'sms' | 'email') => {\n    if (!klant) return;\n\n    const message = `\nFactuur ${factuur.factuurnummer}\n\nBeste ${klant.voornaam} ${klant.achternaam},\n\nHierbij ontvangt u factuur ${factuur.factuurnummer} voor de uitgevoerde werkzaamheden.\n\nFactuurbedrag: €${factuur.totaalInclBtw.toFixed(2)}\nVervaldatum: ${new Date(factuur.vervaldatum).toLocaleDateString('nl-NL')}\n\n${factuur.betaalLink ? `Betalen kan via: ${factuur.betaalLink}` : ''}\n\nMet vriendelijke groet,\n${bedrijfsInstellingen.bedrijfsnaam}\n    `.trim();\n\n    switch (method) {\n      case 'whatsapp':\n        window.open(`https://wa.me/${klant.telefoonnummer.replace(/\\D/g, '')}?text=${encodeURIComponent(message)}`, '_blank');\n        break;\n      case 'sms':\n        window.open(`sms:${klant.telefoonnummer}?body=${encodeURIComponent(message)}`, '_blank');\n        break;\n      case 'email':\n        window.open(`mailto:${klant.email}?subject=${encodeURIComponent(`Factuur ${factuur.factuurnummer}`)}&body=${encodeURIComponent(message)}`, '_blank');\n        break;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Link\n            href=\"/facturen\"\n            className=\"p-2 hover:bg-accent rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-foreground\">\n              Factuur {factuur.factuurnummer}\n            </h1>\n            <div className=\"flex items-center space-x-2 mt-1\">\n              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(factuur.status)}`}>\n                {getStatusLabel(factuur.status)}\n              </span>\n              <span className=\"text-muted-foreground\">\n                Aangemaakt op {new Date(factuur.createdAt).toLocaleDateString('nl-NL')}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={handleGeneratePDF}\n            disabled={isGeneratingPDF}\n            className=\"flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50\"\n          >\n            <Download className=\"h-4 w-4\" />\n            <span>{isGeneratingPDF ? 'Genereren...' : 'PDF Downloaden'}</span>\n          </button>\n\n          <div className=\"flex items-center space-x-1\">\n            <button\n              onClick={() => handleShare('whatsapp')}\n              className=\"p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n              title=\"Delen via WhatsApp\"\n            >\n              <Share2 className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => handleShare('email')}\n              className=\"p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n              title=\"Delen via E-mail\"\n            >\n              <Share2 className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => handleShare('sms')}\n              className=\"p-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\n              title=\"Delen via SMS\"\n            >\n              <Share2 className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          <Link\n            href={`/facturen/${factuur.id}/bewerken`}\n            className=\"flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            <Edit className=\"h-4 w-4\" />\n            <span>Bewerken</span>\n          </Link>\n        </div>\n      </div>\n\n      {/* Factuur Content */}\n      <div id=\"factuur-content\" className=\"bg-white text-black p-8 rounded-lg shadow-lg max-w-4xl mx-auto\">\n        {/* Header */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div>\n            {bedrijfsInstellingen.logo && (\n              <img\n                src={bedrijfsInstellingen.logo}\n                alt=\"Logo\"\n                className=\"h-16 w-auto mb-4\"\n              />\n            )}\n            <h1 className=\"text-3xl font-bold text-blue-600 mb-2\">FACTUUR</h1>\n            <div className=\"text-lg font-semibold\">{factuur.factuurnummer}</div>\n          </div>\n\n          <div className=\"text-right\">\n            <h2 className=\"text-xl font-bold mb-2\">{bedrijfsInstellingen.bedrijfsnaam}</h2>\n            <div className=\"text-sm space-y-1\">\n              <div>{bedrijfsInstellingen.adres}</div>\n              <div>{bedrijfsInstellingen.postcode} {bedrijfsInstellingen.stad}</div>\n              <div>{bedrijfsInstellingen.telefoonnummer}</div>\n              <div>{bedrijfsInstellingen.email}</div>\n              {bedrijfsInstellingen.website && <div>{bedrijfsInstellingen.website}</div>}\n            </div>\n          </div>\n        </div>\n\n        {/* Klant en Factuurgegevens */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3 text-blue-600\">Factuuradres</h3>\n            {klant && (\n              <div className=\"space-y-1\">\n                <div className=\"font-medium\">\n                  {klant.type === 'bedrijf' && klant.bedrijfsnaam\n                    ? klant.bedrijfsnaam\n                    : `${klant.voornaam} ${klant.achternaam}`\n                  }\n                </div>\n                {klant.type === 'bedrijf' && klant.bedrijfsnaam && (\n                  <div>t.a.v. {klant.voornaam} {klant.achternaam}</div>\n                )}\n                <div>{klant.adres} {klant.huisnummer}</div>\n                <div>{klant.postcode} {klant.stad}</div>\n              </div>\n            )}\n          </div>\n\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3 text-blue-600\">Factuurgegevens</h3>\n            <div className=\"space-y-1\">\n              <div><span className=\"font-medium\">Factuurdatum:</span> {new Date(factuur.factuurdatum).toLocaleDateString('nl-NL')}</div>\n              <div><span className=\"font-medium\">Vervaldatum:</span> {new Date(factuur.vervaldatum).toLocaleDateString('nl-NL')}</div>\n              <div><span className=\"font-medium\">Uitvoerdatum:</span> {new Date(factuur.uitvoerdatum).toLocaleDateString('nl-NL')}</div>\n              <div><span className=\"font-medium\">Betalingstermijn:</span> {factuur.betaaltermijn} dagen</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Factuurregels */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-lg font-semibold mb-4 text-blue-600\">Werkzaamheden</h3>\n          <table className=\"w-full border-collapse border border-gray-300\">\n            <thead>\n              <tr className=\"bg-blue-50\">\n                <th className=\"border border-gray-300 px-4 py-2 text-left\">Aantal</th>\n                <th className=\"border border-gray-300 px-4 py-2 text-left\">Beschrijving</th>\n                <th className=\"border border-gray-300 px-4 py-2 text-left\">Uren</th>\n                <th className=\"border border-gray-300 px-4 py-2 text-right\">Prijs/uur</th>\n                <th className=\"border border-gray-300 px-4 py-2 text-right\">Totaal</th>\n              </tr>\n            </thead>\n            <tbody>\n              {factuur.factuurregels.map((regel, index) => (\n                <tr key={regel.id}>\n                  <td className=\"border border-gray-300 px-4 py-2\">{regel.aantal}</td>\n                  <td className=\"border border-gray-300 px-4 py-2\">\n                    <div className=\"font-medium\">{regel.beschrijving}</div>\n                    {regel.detailBeschrijving && (\n                      <div className=\"text-sm text-gray-600 mt-1\">{regel.detailBeschrijving}</div>\n                    )}\n                  </td>\n                  <td className=\"border border-gray-300 px-4 py-2\">{regel.uren}</td>\n                  <td className=\"border border-gray-300 px-4 py-2 text-right\">€{regel.prijsPerUur.toFixed(2)}</td>\n                  <td className=\"border border-gray-300 px-4 py-2 text-right\">€{regel.totaalExclBtw.toFixed(2)}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Extra Kosten */}\n        {(factuur.extraKosten.voorrijkosten > 0 ||\n          factuur.extraKosten.spoedservice > 0 ||\n          factuur.extraKosten.parkeerkosten > 0 ||\n          factuur.extraKosten.materiaalkosten > 0) && (\n          <div className=\"mb-8\">\n            <h3 className=\"text-lg font-semibold mb-4 text-blue-600\">Extra Kosten</h3>\n            <table className=\"w-full border-collapse border border-gray-300\">\n              <thead>\n                <tr className=\"bg-blue-50\">\n                  <th className=\"border border-gray-300 px-4 py-2 text-left\">Beschrijving</th>\n                  <th className=\"border border-gray-300 px-4 py-2 text-right\">Bedrag</th>\n                </tr>\n              </thead>\n              <tbody>\n                {factuur.extraKosten.voorrijkosten > 0 && (\n                  <tr>\n                    <td className=\"border border-gray-300 px-4 py-2\">Voorrijkosten</td>\n                    <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.extraKosten.voorrijkosten.toFixed(2)}</td>\n                  </tr>\n                )}\n                {factuur.extraKosten.spoedservice > 0 && (\n                  <tr>\n                    <td className=\"border border-gray-300 px-4 py-2\">Spoedservice</td>\n                    <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.extraKosten.spoedservice.toFixed(2)}</td>\n                  </tr>\n                )}\n                {factuur.extraKosten.parkeerkosten > 0 && (\n                  <tr>\n                    <td className=\"border border-gray-300 px-4 py-2\">Parkeerkosten</td>\n                    <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.extraKosten.parkeerkosten.toFixed(2)}</td>\n                  </tr>\n                )}\n                {factuur.extraKosten.materiaalkosten > 0 && (\n                  <tr>\n                    <td className=\"border border-gray-300 px-4 py-2\">\n                      <div>Materiaalkosten</div>\n                      {factuur.extraKosten.materiaalDetails && (\n                        <div className=\"text-sm text-gray-600 mt-1\">{factuur.extraKosten.materiaalDetails}</div>\n                      )}\n                    </td>\n                    <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.extraKosten.materiaalkosten.toFixed(2)}</td>\n                  </tr>\n                )}\n              </tbody>\n            </table>\n          </div>\n        )}\n\n        {/* Totalen */}\n        <div className=\"flex justify-end mb-8\">\n          <div className=\"w-80\">\n            <table className=\"w-full border-collapse border border-gray-300\">\n              <tbody>\n                <tr>\n                  <td className=\"border border-gray-300 px-4 py-2 font-medium\">Subtotaal:</td>\n                  <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.subtotaal.toFixed(2)}</td>\n                </tr>\n                {factuur.kortingBedrag > 0 && (\n                  <tr>\n                    <td className=\"border border-gray-300 px-4 py-2 font-medium text-green-600\">\n                      Korting ({factuur.kortingPercentage}%):\n                    </td>\n                    <td className=\"border border-gray-300 px-4 py-2 text-right text-green-600\">\n                      -€{factuur.kortingBedrag.toFixed(2)}\n                    </td>\n                  </tr>\n                )}\n                <tr>\n                  <td className=\"border border-gray-300 px-4 py-2 font-medium\">BTW (21%):</td>\n                  <td className=\"border border-gray-300 px-4 py-2 text-right\">€{factuur.btw21.toFixed(2)}</td>\n                </tr>\n                <tr className=\"bg-blue-50\">\n                  <td className=\"border border-gray-300 px-4 py-2 font-bold text-lg\">TOTAAL INCL. BTW:</td>\n                  <td className=\"border border-gray-300 px-4 py-2 text-right font-bold text-lg\">\n                    €{factuur.totaalInclBtw.toFixed(2)}\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Betalingsinformatie */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3 text-blue-600\">Betalingsinformatie</h3>\n            <div className=\"space-y-1 text-sm\">\n              {bedrijfsInstellingen.ibanNummer && (\n                <div><span className=\"font-medium\">IBAN:</span> {bedrijfsInstellingen.ibanNummer}</div>\n              )}\n              {bedrijfsInstellingen.btwNummer && (\n                <div><span className=\"font-medium\">BTW-nummer:</span> {bedrijfsInstellingen.btwNummer}</div>\n              )}\n              {bedrijfsInstellingen.kvkNummer && (\n                <div><span className=\"font-medium\">KvK-nummer:</span> {bedrijfsInstellingen.kvkNummer}</div>\n              )}\n              <div className=\"mt-3\">\n                <div className=\"font-medium\">{bedrijfsInstellingen.betaalTekst}</div>\n              </div>\n            </div>\n          </div>\n\n          {factuur.betaalLink && (\n            <div>\n              <h3 className=\"text-lg font-semibold mb-3 text-blue-600\">Online Betalen</h3>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-sm\">\n                  Scan de QR-code of gebruik de link om online te betalen\n                </div>\n                {/* QR Code would be generated here */}\n                <div className=\"w-24 h-24 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center text-xs text-gray-500\">\n                  QR Code\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"border-t border-gray-300 pt-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div>\n              <h4 className=\"font-semibold mb-2\">Garantie</h4>\n              <div className=\"text-sm\">\n                {bedrijfsInstellingen.garantieTekst}\n                {bedrijfsInstellingen.garantieDagen > 0 && (\n                  <div className=\"mt-1\">\n                    Garantieperiode: {bedrijfsInstellingen.garantieDagen} dagen vanaf uitvoerdatum.\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <div className=\"text-sm\">\n                {bedrijfsInstellingen.bedankTekst}\n              </div>\n            </div>\n          </div>\n\n          {bedrijfsInstellingen.algemeneVoorwaarden && (\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Algemene Voorwaarden</h4>\n              <div className=\"text-xs text-gray-600\">\n                {bedrijfsInstellingen.algemeneVoorwaarden}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Status Actions */}\n      {factuur.status === 'concept' && (\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h3 className=\"text-lg font-semibold text-foreground mb-4\">Factuur Acties</h3>\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\">\n              <Send className=\"h-4 w-4\" />\n              <span>Factuur Verzenden</span>\n            </button>\n            <button className=\"flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors\">\n              <Euro className=\"h-4 w-4\" />\n              <span>Markeer als Betaald</span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAcA;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,YAAY,OAAO,EAAE;IAC3B,MAAM,UAAU,WAAW;IAC3B,MAAM,QAAQ,UAAU,SAAS,QAAQ,OAAO,IAAI;IAEpD,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAG3D,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,IAAI;YACF,2BAA2B;YAC3B,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,cAAc,CAAC,oJAA2B,EAAE,OAAO;YAEzD,kCAAkC;YAClC,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,CAAC,SAAS;YAEd,4BAA4B;YAC5B,MAAM,SAAS,MAAM,YAAY,SAAS;gBACxC,OAAO;gBACP,SAAS;gBACT,YAAY;YACd;YAEA,aAAa;YACb,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM;YACjC,MAAM,UAAU,OAAO,SAAS,CAAC;YAEjC,MAAM,WAAW,KAAK,iBAAiB;YACvC,MAAM,aAAa,KAAK,kBAAkB;YAC1C,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;YAC3D,IAAI,aAAa;YAEjB,IAAI,WAAW;YAEf,iBAAiB;YACjB,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;YACpD,cAAc;YAEd,iCAAiC;YACjC,MAAO,cAAc,EAAG;gBACtB,WAAW,aAAa;gBACxB,IAAI,OAAO;gBACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;gBACpD,cAAc;YAChB;YAEA,WAAW;YACX,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,IAAI,CAAC;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO;QAEZ,MAAM,UAAU,CAAC;QACb,EAAE,QAAQ,aAAa,CAAC;;MAE1B,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC;;2BAEhB,EAAE,QAAQ,aAAa,CAAC;;gBAEnC,EAAE,QAAQ,aAAa,CAAC,OAAO,CAAC,GAAG;aACtC,EAAE,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC,SAAS;;AAEzE,EAAE,QAAQ,UAAU,GAAG,CAAC,iBAAiB,EAAE,QAAQ,UAAU,EAAE,GAAG,GAAG;;;AAGrE,EAAE,qBAAqB,YAAY,CAAC;IAChC,CAAC,CAAC,IAAI;QAEN,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBAC5G;YACF,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,cAAc,CAAC,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBAC/E;YACF,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,aAAa,EAAE,EAAE,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBAC3I;QACJ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAqC;4CACxC,QAAQ,aAAa;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;0DAC5F,eAAe,QAAQ,MAAM;;;;;;0DAEhC,8OAAC;gDAAK,WAAU;;oDAAwB;oDACvB,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAM,kBAAkB,iBAAiB;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAItB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;gCACxC,WAAU;;kDAEV,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,IAAG;gBAAkB,WAAU;;kCAElC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCACE,qBAAqB,IAAI,kBACxB,8OAAC;wCACC,KAAK,qBAAqB,IAAI;wCAC9B,KAAI;wCACJ,WAAU;;;;;;kDAGd,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAI,WAAU;kDAAyB,QAAQ,aAAa;;;;;;;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B,qBAAqB,YAAY;;;;;;kDACzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK,qBAAqB,KAAK;;;;;;0DAChC,8OAAC;;oDAAK,qBAAqB,QAAQ;oDAAC;oDAAE,qBAAqB,IAAI;;;;;;;0DAC/D,8OAAC;0DAAK,qBAAqB,cAAc;;;;;;0DACzC,8OAAC;0DAAK,qBAAqB,KAAK;;;;;;4CAC/B,qBAAqB,OAAO,kBAAI,8OAAC;0DAAK,qBAAqB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;oCACxD,uBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC3C,MAAM,YAAY,GAClB,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE;;;;;;4CAG5C,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,kBAC7C,8OAAC;;oDAAI;oDAAQ,MAAM,QAAQ;oDAAC;oDAAE,MAAM,UAAU;;;;;;;0DAEhD,8OAAC;;oDAAK,MAAM,KAAK;oDAAC;oDAAE,MAAM,UAAU;;;;;;;0DACpC,8OAAC;;oDAAK,MAAM,QAAQ;oDAAC;oDAAE,MAAM,IAAI;;;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAoB;oDAAE,IAAI,KAAK,QAAQ,YAAY,EAAE,kBAAkB,CAAC;;;;;;;0DAC3G,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAmB;oDAAE,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC;;;;;;;0DACzG,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAoB;oDAAE,IAAI,KAAK,QAAQ,YAAY,EAAE,kBAAkB,CAAC;;;;;;;0DAC3G,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAwB;oDAAE,QAAQ,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;;;;;;;;;;;;kDAGhE,8OAAC;kDACE,QAAQ,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBACjC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC,MAAM,MAAM;;;;;;kEAC9D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EAAe,MAAM,YAAY;;;;;;4DAC/C,MAAM,kBAAkB,kBACvB,8OAAC;gEAAI,WAAU;0EAA8B,MAAM,kBAAkB;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAU;kEAAoC,MAAM,IAAI;;;;;;kEAC5D,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;kEACxF,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,MAAM,aAAa,CAAC,OAAO,CAAC;;;;;;;;+CAVnF,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAkBxB,CAAC,QAAQ,WAAW,CAAC,aAAa,GAAG,KACpC,QAAQ,WAAW,CAAC,YAAY,GAAG,KACnC,QAAQ,WAAW,CAAC,aAAa,GAAG,KACpC,QAAQ,WAAW,CAAC,eAAe,GAAG,CAAC,mBACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;;;;;;;;;;;;kDAGhE,8OAAC;;4CACE,QAAQ,WAAW,CAAC,aAAa,GAAG,mBACnC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,QAAQ,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAG3G,QAAQ,WAAW,CAAC,YAAY,GAAG,mBAClC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,QAAQ,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAG1G,QAAQ,WAAW,CAAC,aAAa,GAAG,mBACnC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,QAAQ,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAG3G,QAAQ,WAAW,CAAC,eAAe,GAAG,mBACrC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAI;;;;;;4DACJ,QAAQ,WAAW,CAAC,gBAAgB,kBACnC,8OAAC;gEAAI,WAAU;0EAA8B,QAAQ,WAAW,CAAC,gBAAgB;;;;;;;;;;;;kEAGrF,8OAAC;wDAAG,WAAU;;4DAA8C;4DAAE,QAAQ,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAStH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAC7D,8OAAC;oDAAG,WAAU;;wDAA8C;wDAAE,QAAQ,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;wCAEzF,QAAQ,aAAa,GAAG,mBACvB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAA8D;wDAChE,QAAQ,iBAAiB;wDAAC;;;;;;;8DAEtC,8OAAC;oDAAG,WAAU;;wDAA6D;wDACtE,QAAQ,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;sDAIvC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAC7D,8OAAC;oDAAG,WAAU;;wDAA8C;wDAAE,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;sDAEtF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAqD;;;;;;8DACnE,8OAAC;oDAAG,WAAU;;wDAAgE;wDAC1E,QAAQ,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;4CACZ,qBAAqB,UAAU,kBAC9B,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAY;oDAAE,qBAAqB,UAAU;;;;;;;4CAEjF,qBAAqB,SAAS,kBAC7B,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAkB;oDAAE,qBAAqB,SAAS;;;;;;;4CAEtF,qBAAqB,SAAS,kBAC7B,8OAAC;;kEAAI,8OAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAkB;oDAAE,qBAAqB,SAAS;;;;;;;0DAEvF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAe,qBAAqB,WAAW;;;;;;;;;;;;;;;;;;;;;;;4BAKnE,QAAQ,UAAU,kBACjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAU;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;0DAAsH;;;;;;;;;;;;;;;;;;;;;;;;kCAS7I,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAI,WAAU;;oDACZ,qBAAqB,aAAa;oDAClC,qBAAqB,aAAa,GAAG,mBACpC,8OAAC;wDAAI,WAAU;;4DAAO;4DACF,qBAAqB,aAAa;4DAAC;;;;;;;;;;;;;;;;;;;kDAM7D,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,qBAAqB,WAAW;;;;;;;;;;;;;;;;;4BAKtC,qBAAqB,mBAAmB,kBACvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDACZ,qBAAqB,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,QAAQ,MAAM,KAAK,2BAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB"}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}