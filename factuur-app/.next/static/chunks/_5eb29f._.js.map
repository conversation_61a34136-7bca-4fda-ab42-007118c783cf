{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/wachtlijst/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { \n  ListTodo, \n  Play, \n  Trash2, \n  Clock, \n  User, \n  Building,\n  AlertCircle\n} from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function WachtlijstPage() {\n  const { \n    wachtlijst, \n    klanten, \n    getKlant,\n    removeFromWachtlijst,\n    resumeFromWachtlijst\n  } = useApp();\n\n  const formatTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const handleResumeFromWachtlijst = (wachtlijstId: string) => {\n    resumeFromWachtlijst(wachtlijstId);\n    // Redirect to tijdregistratie page\n    window.location.href = '/tijdregistratie';\n  };\n\n  const handleRemoveFromWachtlijst = (wachtlijstId: string, klantNaam: string) => {\n    if (confirm(`Weet je zeker dat je ${klantNaam} van de wachtlijst wilt verwijderen?`)) {\n      removeFromWachtlijst(wachtlijstId);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Wachtlijst</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Beheer klanten die tijdelijk op de wachtlijst staan\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2 mt-4 sm:mt-0\">\n          <div className=\"text-sm text-muted-foreground\">\n            {wachtlijst.length} {wachtlijst.length === 1 ? 'item' : 'items'} op wachtlijst\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Totaal Wachtlijst</p>\n              <p className=\"text-2xl font-bold text-foreground\">{wachtlijst.length}</p>\n            </div>\n            <ListTodo className=\"h-8 w-8 text-purple-500\" />\n          </div>\n        </div>\n        \n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Totale Opgeslagen Tijd</p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {formatTime(wachtlijst.reduce((total, item) => total + item.opgeslagenTijd, 0))}\n              </p>\n            </div>\n            <Clock className=\"h-8 w-8 text-orange-500\" />\n          </div>\n        </div>\n\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">Gemiddelde Tijd</p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {wachtlijst.length > 0 \n                  ? formatTime(Math.floor(wachtlijst.reduce((total, item) => total + item.opgeslagenTijd, 0) / wachtlijst.length))\n                  : '00:00:00'\n                }\n              </p>\n            </div>\n            <Clock className=\"h-8 w-8 text-blue-500\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Wachtlijst Items */}\n      <div className=\"bg-card border border-border rounded-lg shadow-sm\">\n        {wachtlijst.length > 0 ? (\n          <div className=\"divide-y divide-border\">\n            {wachtlijst\n              .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n              .map((item) => {\n                const klant = getKlant(item.klantId);\n                \n                return (\n                  <div key={item.id} className=\"p-6 hover:bg-muted/50 transition-colors\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-3\">\n                          <div className={`p-2 rounded-full ${\n                            klant?.type === 'bedrijf' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'\n                          }`}>\n                            {klant?.type === 'bedrijf' ? (\n                              <Building className=\"h-4 w-4\" />\n                            ) : (\n                              <User className=\"h-4 w-4\" />\n                            )}\n                          </div>\n                          <div>\n                            <h3 className=\"text-lg font-semibold text-foreground\">\n                              {klant ? (\n                                klant.type === 'bedrijf' && klant.bedrijfsnaam \n                                  ? klant.bedrijfsnaam \n                                  : `${klant.voornaam} ${klant.achternaam}`\n                              ) : 'Onbekende klant'}\n                            </h3>\n                            {klant?.type === 'bedrijf' && klant.bedrijfsnaam && (\n                              <p className=\"text-sm text-muted-foreground\">\n                                Contactpersoon: {klant.voornaam} {klant.achternaam}\n                              </p>\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm\">\n                          <div>\n                            <span className=\"font-medium text-foreground\">Opgeslagen tijd:</span>\n                            <div className=\"text-2xl font-mono font-bold text-orange-600 mt-1\">\n                              {formatTime(item.opgeslagenTijd)}\n                            </div>\n                          </div>\n                          \n                          <div>\n                            <span className=\"font-medium text-foreground\">Toegevoegd op:</span>\n                            <div className=\"text-muted-foreground mt-1\">\n                              {new Date(item.createdAt).toLocaleDateString('nl-NL', {\n                                day: '2-digit',\n                                month: '2-digit',\n                                year: 'numeric'\n                              })} om {new Date(item.createdAt).toLocaleTimeString('nl-NL', {\n                                hour: '2-digit',\n                                minute: '2-digit'\n                              })}\n                            </div>\n                          </div>\n\n                          {item.beschrijving && (\n                            <div>\n                              <span className=\"font-medium text-foreground\">Beschrijving:</span>\n                              <div className=\"text-muted-foreground mt-1\">\n                                {item.beschrijving}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n\n                        {klant && (\n                          <div className=\"mt-3 text-sm text-muted-foreground\">\n                            <span>{klant.adres} {klant.huisnummer}, {klant.postcode} {klant.stad}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>{klant.telefoonnummer}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>{klant.email}</span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <button\n                          onClick={() => handleResumeFromWachtlijst(item.id)}\n                          className=\"flex items-center space-x-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors\"\n                          title=\"Hervatten\"\n                        >\n                          <Play className=\"h-4 w-4\" />\n                          <span className=\"hidden sm:inline\">Hervatten</span>\n                        </button>\n                        \n                        <button\n                          onClick={() => handleRemoveFromWachtlijst(\n                            item.id, \n                            klant ? `${klant.voornaam} ${klant.achternaam}` : 'Onbekende klant'\n                          )}\n                          className=\"flex items-center space-x-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors\"\n                          title=\"Verwijderen\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                          <span className=\"hidden sm:inline\">Verwijderen</span>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <ListTodo className=\"h-16 w-16 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n              Wachtlijst is leeg\n            </h3>\n            <p className=\"text-muted-foreground mb-6\">\n              Er staan momenteel geen klanten op de wachtlijst. Klanten worden automatisch toegevoegd \n              wanneer je een tijdregistratie pauzeert en op \"Wachtlijst\" klikt.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                href=\"/tijdregistratie\"\n                className=\"inline-flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                <Clock className=\"h-4 w-4\" />\n                <span>Ga naar Tijdregistratie</span>\n              </Link>\n              <Link\n                href=\"/klanten\"\n                className=\"inline-flex items-center space-x-2 bg-secondary text-secondary-foreground px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors\"\n              >\n                <User className=\"h-4 w-4\" />\n                <span>Beheer Klanten</span>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Help Section */}\n      <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n        <div className=\"flex items-start space-x-3\">\n          <AlertCircle className=\"h-5 w-5 text-blue-500 mt-0.5\" />\n          <div>\n            <h3 className=\"text-lg font-semibold text-foreground mb-2\">Hoe werkt de wachtlijst?</h3>\n            <div className=\"text-sm text-muted-foreground space-y-2\">\n              <p>\n                • De wachtlijst is handig wanneer je tijdelijk moet stoppen met werken aan een klus, \n                maar de tijd wilt bewaren voor later.\n              </p>\n              <p>\n                • Klik op \"Wachtlijst\" bij een actieve tijdregistratie om deze op te slaan.\n              </p>\n              <p>\n                • Gebruik \"Hervatten\" om de tijdregistratie weer te activeren met de opgeslagen tijd.\n              </p>\n              <p>\n                • De opgeslagen tijd wordt automatisch toegevoegd aan de nieuwe tijdregistratie.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      {wachtlijst.length > 0 && (\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h3 className=\"text-lg font-semibold text-foreground mb-4\">Snelle Acties</h3>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <button\n              onClick={() => {\n                if (confirm('Weet je zeker dat je alle items van de wachtlijst wilt verwijderen?')) {\n                  wachtlijst.forEach(item => removeFromWachtlijst(item.id));\n                }\n              }}\n              className=\"flex items-center justify-center p-4 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\"\n            >\n              <Trash2 className=\"h-5 w-5 mr-2\" />\n              Wachtlijst Legen\n            </button>\n            <Link\n              href=\"/tijdregistratie\"\n              className=\"flex items-center justify-center p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n            >\n              <Clock className=\"h-5 w-5 mr-2\" />\n              Nieuwe Tijdregistratie\n            </Link>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAee,SAAS;;IACtB,MAAM,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,oBAAoB,EACpB,oBAAoB,EACrB,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAET,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC1H;IAEA,MAAM,6BAA6B,CAAC;QAClC,qBAAqB;QACrB,mCAAmC;QACnC,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,6BAA6B,CAAC,cAAsB;QACxD,IAAI,QAAQ,CAAC,qBAAqB,EAAE,UAAU,oCAAoC,CAAC,GAAG;YACpF,qBAAqB;QACvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,MAAM;gCAAC;gCAAE,WAAW,MAAM,KAAK,IAAI,SAAS;gCAAQ;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAsC,WAAW,MAAM;;;;;;;;;;;;8CAEtE,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDACV,WAAW,WAAW,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,cAAc,EAAE;;;;;;;;;;;;8CAGhF,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,GAAG,IACjB,WAAW,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,cAAc,EAAE,KAAK,WAAW,MAAM,KAC5G;;;;;;;;;;;;8CAIR,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,GAAG,kBACnB,6LAAC;oBAAI,WAAU;8BACZ,WACE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,GAAG,CAAC,CAAC;wBACJ,MAAM,QAAQ,SAAS,KAAK,OAAO;wBAEnC,qBACE,6LAAC;4BAAkB,WAAU;sCAC3B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,iBAAiB,EAChC,OAAO,SAAS,YAAY,gCAAgC,6BAC5D;kEACC,OAAO,SAAS,0BACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;iFAEpB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAGpB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QACC,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC1C,MAAM,YAAY,GAClB,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE,GACzC;;;;;;4DAEL,OAAO,SAAS,aAAa,MAAM,YAAY,kBAC9C,6LAAC;gEAAE,WAAU;;oEAAgC;oEAC1B,MAAM,QAAQ;oEAAC;oEAAE,MAAM,UAAU;;;;;;;;;;;;;;;;;;;0DAM1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAI,WAAU;0EACZ,WAAW,KAAK,cAAc;;;;;;;;;;;;kEAInC,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAI,WAAU;;oEACZ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;wEACpD,KAAK;wEACL,OAAO;wEACP,MAAM;oEACR;oEAAG;oEAAK,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;wEAC3D,MAAM;wEACN,QAAQ;oEACV;;;;;;;;;;;;;oDAIH,KAAK,YAAY,kBAChB,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAI,WAAU;0EACZ,KAAK,YAAY;;;;;;;;;;;;;;;;;;4CAMzB,uBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAM,MAAM,KAAK;4DAAC;4DAAE,MAAM,UAAU;4DAAC;4DAAG,MAAM,QAAQ;4DAAC;4DAAE,MAAM,IAAI;;;;;;;kEACpE,6LAAC;wDAAK,WAAU;kEAAO;;;;;;kEACvB,6LAAC;kEAAM,MAAM,cAAc;;;;;;kEAC3B,6LAAC;wDAAK,WAAU;kEAAO;;;;;;kEACvB,6LAAC;kEAAM,MAAM,KAAK;;;;;;;;;;;;;;;;;;kDAMxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,2BAA2B,KAAK,EAAE;gDACjD,WAAU;gDACV,OAAM;;kEAEN,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAGrC,6LAAC;gDACC,SAAS,IAAM,2BACb,KAAK,EAAE,EACP,QAAQ,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE,GAAG;gDAEpD,WAAU;gDACV,OAAM;;kEAEN,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;2BA5FjC,KAAK,EAAE;;;;;oBAkGrB;;;;;yCAGJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDAIH,6LAAC;sDAAE;;;;;;sDAGH,6LAAC;sDAAE;;;;;;sDAGH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASV,WAAW,MAAM,GAAG,mBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;oCACP,IAAI,QAAQ,wEAAwE;wCAClF,WAAW,OAAO,CAAC,CAAA,OAAQ,qBAAqB,KAAK,EAAE;oCACzD;gCACF;gCACA,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAtRwB;;QAOlB,iIAAA,CAAA,SAAM;;;KAPY"}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "file": "building.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/building.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', ry: '2', key: '76otgf' }],\n  ['path', { d: 'M9 22v-4h6v4', key: 'r93iot' }],\n  ['path', { d: 'M8 6h.01', key: '1dz90k' }],\n  ['path', { d: 'M16 6h.01', key: '1x0f13' }],\n  ['path', { d: 'M12 6h.01', key: '1vi96p' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n];\n\n/**\n * @component @name Building\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNOSAyMnYtNGg2djQiIC8+CiAgPHBhdGggZD0iTTggNmguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNOCAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/building\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building = createLucideIcon('building', __iconNode);\n\nexport default Building;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC7C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}