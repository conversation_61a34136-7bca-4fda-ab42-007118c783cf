{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/utils/berekeningPdfGenerator.ts"], "sourcesContent": ["import { jsPDF } from 'jspdf';\nimport QRCode from 'qrcode';\n\ninterface BerekeningData {\n  klant: any;\n  serviceType: string;\n  customService?: string;\n  klusbeschrijving: string;\n  totalUren: number;\n  uurtarief: number;\n  arbeidskosten: number;\n  extraKosten: number;\n  subtotaal: number;\n  kortingBedrag: number;\n  kortingPercentage: number;\n  btw21: number;\n  totaalInclBtw: number;\n  bedrijfsInstellingen: any;\n  formData: any;\n}\n\nexport const generateBerekeningPDF = async (berekeningData: BerekeningData) => {\n  const pdf = new jsPDF('p', 'mm', 'a4');\n  const pageWidth = 210;\n  const pageHeight = 297;\n\n  // Colors\n  const blueColor = [54, 96, 191]; // #3660BF\n  const lightBlueColor = [240, 243, 251];\n  const darkGrayColor = [51, 51, 51];\n  const lightGrayColor = [128, 128, 128];\n\n  // Helper function to add text with proper encoding\n  const addText = (text: string, x: number, y: number, options: any = {}) => {\n    pdf.setFont(options.font || 'helvetica', options.style || 'normal');\n    pdf.setFontSize(options.size || 10);\n    if (options.color) {\n      pdf.setTextColor(...options.color);\n    }\n    pdf.text(text, x, y, options.align ? { align: options.align } : undefined);\n  };\n\n  // Colors matching the template\n  const primaryBlue = [37, 99, 235]; // #2563eb\n  const darkBlue = [30, 58, 138]; // #1e3a8a\n  const lightBlue = [240, 249, 255]; // #f0f9ff\n  const gray50 = [249, 250, 251]; // #f9fafb\n  const gray600 = [75, 85, 99]; // #4b5563\n  const gray700 = [55, 65, 81]; // #374151\n  const gray800 = [31, 41, 55]; // #1f2937\n  const gray900 = [17, 24, 39]; // #111827\n\n  // Helper functions\n  const addGradientRect = (x: number, y: number, width: number, height: number) => {\n    const steps = 20;\n    for (let i = 0; i < steps; i++) {\n      const ratio = i / steps;\n      const r = primaryBlue[0] + (darkBlue[0] - primaryBlue[0]) * ratio;\n      const g = primaryBlue[1] + (darkBlue[1] - primaryBlue[1]) * ratio;\n      const b = primaryBlue[2] + (darkBlue[2] - primaryBlue[2]) * ratio;\n\n      pdf.setFillColor(r, g, b);\n      pdf.rect(x, y + (height * ratio), width, height / steps, 'F');\n    }\n  };\n\n  // Header with gradient background\n  addGradientRect(0, 0, pageWidth, 25);\n\n  // KOSTENBEREKENING title in white\n  addText('KOSTENBEREKENING', 15, 15, { color: [255, 255, 255], style: 'bold', size: 18 });\n\n  // Contact info in header\n  const contactY = 20;\n  addText('📞 020-1234567', 15, contactY, { color: [255, 255, 255], size: 8 });\n  addText('✉ <EMAIL>', 70, contactY, { color: [255, 255, 255], size: 8 });\n  addText('🌐 www.klusexperts.nl', 140, contactY, { color: [255, 255, 255], size: 8 });\n\n  // Company logo area (if available)\n  if (berekeningData.bedrijfsInstellingen.logo) {\n    // Logo would be added here - for now we'll add company name\n    pdf.setFont('helvetica', 'bold');\n    pdf.setFontSize(14);\n    pdf.text(berekeningData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 20, { align: 'right' });\n  }\n\n  // Reset text color\n  pdf.setTextColor(...darkGrayColor);\n\n  // Company details (right side)\n  let yPos = 45;\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n\n  const companyDetails = [\n    berekeningData.bedrijfsInstellingen.bedrijfsnaam,\n    berekeningData.bedrijfsInstellingen.adres,\n    `${berekeningData.bedrijfsInstellingen.postcode} ${berekeningData.bedrijfsInstellingen.stad}`,\n    berekeningData.bedrijfsInstellingen.telefoonnummer,\n    berekeningData.bedrijfsInstellingen.email,\n    berekeningData.bedrijfsInstellingen.website || ''\n  ].filter(Boolean);\n\n  companyDetails.forEach((detail, index) => {\n    pdf.text(detail, pageWidth - 15, yPos + (index * 4), { align: 'right' });\n  });\n\n  // Customer details (left side)\n  yPos = 45;\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(10);\n  pdf.text('Klantgegevens:', 15, yPos);\n\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n  yPos += 6;\n\n  const customerName = berekeningData.klant.type === 'bedrijf' && berekeningData.klant.bedrijfsnaam\n    ? berekeningData.klant.bedrijfsnaam\n    : `${berekeningData.klant.voornaam} ${berekeningData.klant.achternaam}`;\n\n  const customerDetails = [\n    customerName,\n    berekeningData.klant.type === 'bedrijf' && berekeningData.klant.bedrijfsnaam\n      ? `t.a.v. ${berekeningData.klant.voornaam} ${berekeningData.klant.achternaam}`\n      : '',\n    `${berekeningData.klant.adres} ${berekeningData.klant.huisnummer}`,\n    `${berekeningData.klant.postcode} ${berekeningData.klant.stad}`\n  ].filter(Boolean);\n\n  customerDetails.forEach((detail, index) => {\n    pdf.text(detail, 15, yPos + (index * 4));\n  });\n\n  // Service details box (right side)\n  const boxX = pageWidth - 80;\n  const boxY = 70;\n  const boxWidth = 65;\n  const boxHeight = 25;\n\n  // Light blue background for service details\n  pdf.setFillColor(...lightBlueColor);\n  pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');\n\n  // Service details\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n  pdf.setTextColor(...darkGrayColor);\n\n  const serviceLabel = berekeningData.serviceType === 'custom'\n    ? berekeningData.customService\n    : berekeningData.serviceType.charAt(0).toUpperCase() + berekeningData.serviceType.slice(1);\n\n  const serviceDetails = [\n    ['Service:', serviceLabel || ''],\n    ['Datum:', new Date().toLocaleDateString('nl-NL')],\n    ['Uren:', berekeningData.totalUren.toFixed(2)],\n    ['Tarief:', `€ ${berekeningData.uurtarief.toFixed(2)}`]\n  ];\n\n  serviceDetails.forEach((detail, index) => {\n    pdf.text(detail[0], boxX + 2, boxY + 6 + (index * 4));\n    pdf.setFont('helvetica', 'normal');\n    pdf.text(detail[1], boxX + 20, boxY + 6 + (index * 4));\n    pdf.setFont('helvetica', 'bold');\n  });\n\n  // Work description\n  yPos = 105;\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(10);\n  pdf.text('Werkzaamheden:', 15, yPos);\n\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n  yPos += 6;\n\n  if (berekeningData.klusbeschrijving) {\n    const descriptionLines = pdf.splitTextToSize(berekeningData.klusbeschrijving, pageWidth - 30);\n    descriptionLines.forEach((line: string, index: number) => {\n      pdf.text(line, 15, yPos + (index * 4));\n    });\n    yPos += descriptionLines.length * 4 + 10;\n  } else {\n    yPos += 10;\n  }\n\n  // Cost breakdown table\n  const tableStartY = yPos;\n\n  // Blue header background\n  pdf.setFillColor(...blueColor);\n  pdf.rect(15, yPos, pageWidth - 30, 8, 'F');\n\n  // Table headers in white\n  pdf.setTextColor(255, 255, 255);\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n\n  const headers = ['Beschrijving', 'Uren', 'Tarief', 'Bedrag'];\n  const colWidths = [80, 30, 30, 40];\n  let xPos = 15;\n\n  headers.forEach((header, index) => {\n    const headerX = index === 0 ? xPos + 2 : xPos + colWidths[index] - 2;\n    const align = index === 0 ? undefined : { align: 'right' };\n    pdf.text(header, headerX, yPos + 5, align);\n    xPos += colWidths[index];\n  });\n\n  // Table rows\n  yPos += 8;\n  pdf.setTextColor(...darkGrayColor);\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n\n  const costRows = [\n    ['Arbeidskosten', berekeningData.totalUren.toFixed(2), `€ ${berekeningData.uurtarief.toFixed(2)}`, `€ ${berekeningData.arbeidskosten.toFixed(2)}`]\n  ];\n\n  // Add extra costs if any\n  if (berekeningData.formData.voorrijkosten > 0) {\n    costRows.push(['Voorrijkosten', '1', `€ ${berekeningData.formData.voorrijkosten.toFixed(2)}`, `€ ${berekeningData.formData.voorrijkosten.toFixed(2)}`]);\n  }\n  if (berekeningData.formData.spoedservice > 0) {\n    costRows.push(['Spoedservice', '1', `€ ${berekeningData.formData.spoedservice.toFixed(2)}`, `€ ${berekeningData.formData.spoedservice.toFixed(2)}`]);\n  }\n  if (berekeningData.formData.parkeerkosten > 0) {\n    costRows.push(['Parkeerkosten', '1', `€ ${berekeningData.formData.parkeerkosten.toFixed(2)}`, `€ ${berekeningData.formData.parkeerkosten.toFixed(2)}`]);\n  }\n  if (berekeningData.formData.materiaalkosten > 0) {\n    costRows.push(['Materiaalkosten', '1', `€ ${berekeningData.formData.materiaalkosten.toFixed(2)}`, `€ ${berekeningData.formData.materiaalkosten.toFixed(2)}`]);\n  }\n\n  costRows.forEach((row, index) => {\n    const rowY = yPos + (index * 6);\n\n    // Alternating row colors\n    if (index % 2 === 1) {\n      pdf.setFillColor(248, 249, 250);\n      pdf.rect(15, rowY, pageWidth - 30, 6, 'F');\n    }\n\n    xPos = 15;\n    row.forEach((data, colIndex) => {\n      const align = colIndex === 0 ? 'left' : 'right';\n      const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 : xPos + 2;\n\n      pdf.text(data, textX, rowY + 3, align !== 'left' ? { align } : undefined);\n      xPos += colWidths[colIndex];\n    });\n  });\n\n  // Totals section\n  const totalsY = yPos + (costRows.length * 6) + 10;\n  const totalsX = pageWidth - 80;\n\n  // Totals background\n  pdf.setFillColor(...lightBlueColor);\n  pdf.rect(totalsX, totalsY, 65, 30, 'F');\n\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n\n  const totals = [\n    ['Subtotaal:', `€ ${berekeningData.subtotaal.toFixed(2)}`],\n    ...(berekeningData.kortingBedrag > 0 ? [['Korting:', `€ ${berekeningData.kortingBedrag.toFixed(2)}`]] : []),\n    ['BTW 21%:', `€ ${berekeningData.btw21.toFixed(2)}`],\n    ['', ''], // Empty line\n    ['Totaal incl. BTW:', `€ ${berekeningData.totaalInclBtw.toFixed(2)}`]\n  ];\n\n  totals.forEach((total, index) => {\n    const isTotal = total[0] === 'Totaal incl. BTW:';\n    if (isTotal) {\n      pdf.setFont('helvetica', 'bold');\n      pdf.setFontSize(11);\n    }\n\n    if (total[0]) {\n      pdf.text(total[0], totalsX + 2, totalsY + 6 + (index * 5));\n      pdf.text(total[1], totalsX + 63, totalsY + 6 + (index * 5), { align: 'right' });\n    }\n\n    if (isTotal) {\n      pdf.setFont('helvetica', 'normal');\n      pdf.setFontSize(9);\n    }\n  });\n\n  // Footer\n  const footerY = pageHeight - 40;\n\n  // Note\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n  pdf.text('Opmerking:', 15, footerY);\n\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n  pdf.text('Dit is een kostenberekening en geen officiële factuur.', 15, footerY + 5);\n  pdf.text('Voor een officiële factuur kunt u contact met ons opnemen.', 15, footerY + 10);\n\n  // Company info\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n\n  const footerInfo = [\n    berekeningData.bedrijfsInstellingen.telefoonnummer ? `Tel: ${berekeningData.bedrijfsInstellingen.telefoonnummer}` : '',\n    berekeningData.bedrijfsInstellingen.email ? `Email: ${berekeningData.bedrijfsInstellingen.email}` : '',\n    berekeningData.bedrijfsInstellingen.website ? `Web: ${berekeningData.bedrijfsInstellingen.website}` : ''\n  ].filter(Boolean);\n\n  footerInfo.forEach((info, index) => {\n    pdf.text(info, 15, footerY + 20 + (index * 4));\n  });\n\n  return pdf;\n};\n"], "names": [], "mappings": ";;;AAAA;;AAqBO,MAAM,wBAAwB,OAAO;IAC1C,MAAM,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC,KAAK,MAAM;IACjC,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,SAAS;IACT,MAAM,YAAY;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC3C,MAAM,iBAAiB;QAAC;QAAK;QAAK;KAAI;IACtC,MAAM,gBAAgB;QAAC;QAAI;QAAI;KAAG;IAClC,MAAM,iBAAiB;QAAC;QAAK;QAAK;KAAI;IAEtC,mDAAmD;IACnD,MAAM,UAAU,CAAC,MAAc,GAAW,GAAW,UAAe,CAAC,CAAC;QACpE,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,aAAa,QAAQ,KAAK,IAAI;QAC1D,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI;QAChC,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,YAAY,IAAI,QAAQ,KAAK;QACnC;QACA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,QAAQ,KAAK,GAAG;YAAE,OAAO,QAAQ,KAAK;QAAC,IAAI;IAClE;IAEA,+BAA+B;IAC/B,MAAM,cAAc;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC7C,MAAM,WAAW;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC1C,MAAM,YAAY;QAAC;QAAK;QAAK;KAAI,EAAE,UAAU;IAC7C,MAAM,SAAS;QAAC;QAAK;QAAK;KAAI,EAAE,UAAU;IAC1C,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IACxC,MAAM,UAAU;QAAC;QAAI;QAAI;KAAG,EAAE,UAAU;IAExC,mBAAmB;IACnB,MAAM,kBAAkB,CAAC,GAAW,GAAW,OAAe;QAC5D,MAAM,QAAQ;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,QAAQ,IAAI;YAClB,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAC5D,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAC5D,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;YAE5D,IAAI,YAAY,CAAC,GAAG,GAAG;YACvB,IAAI,IAAI,CAAC,GAAG,IAAK,SAAS,OAAQ,OAAO,SAAS,OAAO;QAC3D;IACF;IAEA,kCAAkC;IAClC,gBAAgB,GAAG,GAAG,WAAW;IAEjC,kCAAkC;IAClC,QAAQ,oBAAoB,IAAI,IAAI;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,OAAO;QAAQ,MAAM;IAAG;IAEtF,yBAAyB;IACzB,MAAM,WAAW;IACjB,QAAQ,kBAAkB,IAAI,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IAC1E,QAAQ,yBAAyB,IAAI,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IACjF,QAAQ,yBAAyB,KAAK,UAAU;QAAE,OAAO;YAAC;YAAK;YAAK;SAAI;QAAE,MAAM;IAAE;IAElF,mCAAmC;IACnC,IAAI,eAAe,oBAAoB,CAAC,IAAI,EAAE;QAC5C,4DAA4D;QAC5D,IAAI,OAAO,CAAC,aAAa;QACzB,IAAI,WAAW,CAAC;QAChB,IAAI,IAAI,CAAC,eAAe,oBAAoB,CAAC,YAAY,EAAE,YAAY,IAAI,IAAI;YAAE,OAAO;QAAQ;IAClG;IAEA,mBAAmB;IACnB,IAAI,YAAY,IAAI;IAEpB,+BAA+B;IAC/B,IAAI,OAAO;IACX,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,iBAAiB;QACrB,eAAe,oBAAoB,CAAC,YAAY;QAChD,eAAe,oBAAoB,CAAC,KAAK;QACzC,GAAG,eAAe,oBAAoB,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,oBAAoB,CAAC,IAAI,EAAE;QAC7F,eAAe,oBAAoB,CAAC,cAAc;QAClD,eAAe,oBAAoB,CAAC,KAAK;QACzC,eAAe,oBAAoB,CAAC,OAAO,IAAI;KAChD,CAAC,MAAM,CAAC;IAET,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,IAAI,IAAI,CAAC,QAAQ,YAAY,IAAI,OAAQ,QAAQ,GAAI;YAAE,OAAO;QAAQ;IACxE;IAEA,+BAA+B;IAC/B,OAAO;IACP,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI;IAE/B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,QAAQ;IAER,MAAM,eAAe,eAAe,KAAK,CAAC,IAAI,KAAK,aAAa,eAAe,KAAK,CAAC,YAAY,GAC7F,eAAe,KAAK,CAAC,YAAY,GACjC,GAAG,eAAe,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC,UAAU,EAAE;IAEzE,MAAM,kBAAkB;QACtB;QACA,eAAe,KAAK,CAAC,IAAI,KAAK,aAAa,eAAe,KAAK,CAAC,YAAY,GACxE,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC,UAAU,EAAE,GAC5E;QACJ,GAAG,eAAe,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC,UAAU,EAAE;QAClE,GAAG,eAAe,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC,IAAI,EAAE;KAChE,CAAC,MAAM,CAAC;IAET,gBAAgB,OAAO,CAAC,CAAC,QAAQ;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAQ,QAAQ;IACvC;IAEA,mCAAmC;IACnC,MAAM,OAAO,YAAY;IACzB,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,YAAY;IAElB,4CAA4C;IAC5C,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,MAAM,MAAM,UAAU,WAAW;IAE1C,kBAAkB;IAClB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,YAAY,IAAI;IAEpB,MAAM,eAAe,eAAe,WAAW,KAAK,WAChD,eAAe,aAAa,GAC5B,eAAe,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,eAAe,WAAW,CAAC,KAAK,CAAC;IAE1F,MAAM,iBAAiB;QACrB;YAAC;YAAY,gBAAgB;SAAG;QAChC;YAAC;YAAU,IAAI,OAAO,kBAAkB,CAAC;SAAS;QAClD;YAAC;YAAS,eAAe,SAAS,CAAC,OAAO,CAAC;SAAG;QAC9C;YAAC;YAAW,CAAC,EAAE,EAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI;SAAC;KACxD;IAED,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,GAAG,OAAO,IAAK,QAAQ;QAClD,IAAI,OAAO,CAAC,aAAa;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,IAAK,QAAQ;QACnD,IAAI,OAAO,CAAC,aAAa;IAC3B;IAEA,mBAAmB;IACnB,OAAO;IACP,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI;IAE/B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,QAAQ;IAER,IAAI,eAAe,gBAAgB,EAAE;QACnC,MAAM,mBAAmB,IAAI,eAAe,CAAC,eAAe,gBAAgB,EAAE,YAAY;QAC1F,iBAAiB,OAAO,CAAC,CAAC,MAAc;YACtC,IAAI,IAAI,CAAC,MAAM,IAAI,OAAQ,QAAQ;QACrC;QACA,QAAQ,iBAAiB,MAAM,GAAG,IAAI;IACxC,OAAO;QACL,QAAQ;IACV;IAEA,uBAAuB;IACvB,MAAM,cAAc;IAEpB,yBAAyB;IACzB,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;IAEtC,yBAAyB;IACzB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,UAAU;QAAC;QAAgB;QAAQ;QAAU;KAAS;IAC5D,MAAM,YAAY;QAAC;QAAI;QAAI;QAAI;KAAG;IAClC,IAAI,OAAO;IAEX,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACvB,MAAM,UAAU,UAAU,IAAI,OAAO,IAAI,OAAO,SAAS,CAAC,MAAM,GAAG;QACnE,MAAM,QAAQ,UAAU,IAAI,YAAY;YAAE,OAAO;QAAQ;QACzD,IAAI,IAAI,CAAC,QAAQ,SAAS,OAAO,GAAG;QACpC,QAAQ,SAAS,CAAC,MAAM;IAC1B;IAEA,aAAa;IACb,QAAQ;IACR,IAAI,YAAY,IAAI;IACpB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,WAAW;QACf;YAAC;YAAiB,eAAe,SAAS,CAAC,OAAO,CAAC;YAAI,CAAC,EAAE,EAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI;YAAE,CAAC,EAAE,EAAE,eAAe,aAAa,CAAC,OAAO,CAAC,IAAI;SAAC;KACnJ;IAED,yBAAyB;IACzB,IAAI,eAAe,QAAQ,CAAC,aAAa,GAAG,GAAG;QAC7C,SAAS,IAAI,CAAC;YAAC;YAAiB;YAAK,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI;YAAE,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI;SAAC;IACxJ;IACA,IAAI,eAAe,QAAQ,CAAC,YAAY,GAAG,GAAG;QAC5C,SAAS,IAAI,CAAC;YAAC;YAAgB;YAAK,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;YAAE,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;SAAC;IACrJ;IACA,IAAI,eAAe,QAAQ,CAAC,aAAa,GAAG,GAAG;QAC7C,SAAS,IAAI,CAAC;YAAC;YAAiB;YAAK,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI;YAAE,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI;SAAC;IACxJ;IACA,IAAI,eAAe,QAAQ,CAAC,eAAe,GAAG,GAAG;QAC/C,SAAS,IAAI,CAAC;YAAC;YAAmB;YAAK,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI;YAAE,CAAC,EAAE,EAAE,eAAe,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI;SAAC;IAC9J;IAEA,SAAS,OAAO,CAAC,CAAC,KAAK;QACrB,MAAM,OAAO,OAAQ,QAAQ;QAE7B,yBAAyB;QACzB,IAAI,QAAQ,MAAM,GAAG;YACnB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;QACxC;QAEA,OAAO;QACP,IAAI,OAAO,CAAC,CAAC,MAAM;YACjB,MAAM,QAAQ,aAAa,IAAI,SAAS;YACxC,MAAM,QAAQ,UAAU,UAAU,OAAO,SAAS,CAAC,SAAS,GAAG,IAAI,OAAO;YAE1E,IAAI,IAAI,CAAC,MAAM,OAAO,OAAO,GAAG,UAAU,SAAS;gBAAE;YAAM,IAAI;YAC/D,QAAQ,SAAS,CAAC,SAAS;QAC7B;IACF;IAEA,iBAAiB;IACjB,MAAM,UAAU,OAAQ,SAAS,MAAM,GAAG,IAAK;IAC/C,MAAM,UAAU,YAAY;IAE5B,oBAAoB;IACpB,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,SAAS,SAAS,IAAI,IAAI;IAEnC,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,SAAS;QACb;YAAC;YAAc,CAAC,EAAE,EAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI;SAAC;WACtD,eAAe,aAAa,GAAG,IAAI;YAAC;gBAAC;gBAAY,CAAC,EAAE,EAAE,eAAe,aAAa,CAAC,OAAO,CAAC,IAAI;aAAC;SAAC,GAAG,EAAE;QAC1G;YAAC;YAAY,CAAC,EAAE,EAAE,eAAe,KAAK,CAAC,OAAO,CAAC,IAAI;SAAC;QACpD;YAAC;YAAI;SAAG;QACR;YAAC;YAAqB,CAAC,EAAE,EAAE,eAAe,aAAa,CAAC,OAAO,CAAC,IAAI;SAAC;KACtE;IAED,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,MAAM,UAAU,KAAK,CAAC,EAAE,KAAK;QAC7B,IAAI,SAAS;YACX,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;QAClB;QAEA,IAAI,KAAK,CAAC,EAAE,EAAE;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,UAAU,IAAK,QAAQ;YACvD,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,IAAI,UAAU,IAAK,QAAQ,GAAI;gBAAE,OAAO;YAAQ;QAC/E;QAEA,IAAI,SAAS;YACX,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;QAClB;IACF;IAEA,SAAS;IACT,MAAM,UAAU,aAAa;IAE7B,OAAO;IACP,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,cAAc,IAAI;IAE3B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,0DAA0D,IAAI,UAAU;IACjF,IAAI,IAAI,CAAC,8DAA8D,IAAI,UAAU;IAErF,eAAe;IACf,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,aAAa;QACjB,eAAe,oBAAoB,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,eAAe,oBAAoB,CAAC,cAAc,EAAE,GAAG;QACpH,eAAe,oBAAoB,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,eAAe,oBAAoB,CAAC,KAAK,EAAE,GAAG;QACpG,eAAe,oBAAoB,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,eAAe,oBAAoB,CAAC,OAAO,EAAE,GAAG;KACvG,CAAC,MAAM,CAAC;IAET,WAAW,OAAO,CAAC,CAAC,MAAM;QACxB,IAAI,IAAI,CAAC,MAAM,IAAI,UAAU,KAAM,QAAQ;IAC7C;IAEA,OAAO;AACT"}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}