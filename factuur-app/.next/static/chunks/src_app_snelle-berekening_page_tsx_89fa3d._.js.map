{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/snelle-berekening/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { \n  Calculator, \n  User, \n  Building, \n  Euro,\n  FileText,\n  Eye,\n  Share2,\n  Download\n} from 'lucide-react';\n\nexport default function SnelleBerekeningPage() {\n  const { klanten, bedrijfsInstellingen } = useApp();\n  \n  const [selectedKlantId, setSelectedKlantId] = useState('');\n  const [formData, setFormData] = useState({\n    serviceType: 'loodgieter' as const,\n    customService: '',\n    klusbeschrijving: '',\n    uurtarief: bedrijfsInstellingen.standaardUurtarief,\n    uren: 0,\n    minuten: 0,\n    btw21Percentage: bedrijfsInstellingen.standaardBtw21,\n    btw9Percentage: bedrijfsInstellingen.standaardBtw9,\n    voorrijkosten: bedrijfsInstellingen.standaardVoorrijkosten,\n    spoedservice: bedrijfsInstellingen.standaardSpoedservice,\n    parkeerkosten: 0,\n    materiaalkosten: 0,\n    materiaalDetails: '',\n    kortingPercentage: 0,\n  });\n\n  const [berekening, setBerekening] = useState<any>(null);\n  const [showPreview, setShowPreview] = useState(false);\n\n  const serviceTypes = [\n    { value: 'loodgieter', label: 'Loodgieter' },\n    { value: 'elektricien', label: 'Elektricien' },\n    { value: 'klusjesman', label: 'Klusjesman' },\n    { value: 'timmerman', label: 'Timmerman' },\n    { value: 'aannemer', label: 'Aannemer' },\n    { value: 'custom', label: 'Anders...' },\n  ];\n\n  const selectedKlant = klanten.find(k => k.id === selectedKlantId);\n  const totalUren = formData.uren + (formData.minuten / 60);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || 0 : value\n    }));\n  };\n\n  const berekenTotaal = () => {\n    const arbeidskosten = totalUren * formData.uurtarief;\n    const extraKosten = formData.voorrijkosten + formData.spoedservice + formData.parkeerkosten + formData.materiaalkosten;\n    const subtotaal = arbeidskosten + extraKosten;\n    \n    const kortingBedrag = (subtotaal * formData.kortingPercentage) / 100;\n    const subtotaalNaKorting = subtotaal - kortingBedrag;\n    \n    // Voor eenvoud: alle kosten onder 21% BTW\n    const btw21 = (subtotaalNaKorting * formData.btw21Percentage) / 100;\n    const btw9 = 0; // Voor deze berekening gebruiken we alleen 21% BTW\n    \n    const totaalInclBtw = subtotaalNaKorting + btw21 + btw9;\n\n    const result = {\n      arbeidskosten,\n      extraKosten,\n      subtotaal,\n      kortingBedrag,\n      subtotaalNaKorting,\n      btw21,\n      btw9,\n      totaalInclBtw,\n      klant: selectedKlant,\n      formData: { ...formData, totalUren }\n    };\n\n    setBerekening(result);\n    return result;\n  };\n\n  const handleBerekenen = () => {\n    if (!selectedKlantId) {\n      alert('Selecteer eerst een klant');\n      return;\n    }\n    if (totalUren === 0) {\n      alert('Voer een geldige tijd in');\n      return;\n    }\n    berekenTotaal();\n  };\n\n  const handlePreview = () => {\n    if (berekening) {\n      setShowPreview(true);\n    }\n  };\n\n  const handleShare = (method: 'whatsapp' | 'sms' | 'email') => {\n    if (!berekening || !selectedKlant) return;\n\n    const message = `\nKostenberekening voor ${selectedKlant.voornaam} ${selectedKlant.achternaam}\n\nService: ${formData.serviceType === 'custom' ? formData.customService : serviceTypes.find(s => s.value === formData.serviceType)?.label}\nBeschrijving: ${formData.klusbeschrijving}\n\nArbeidskosten: ${totalUren.toFixed(2)} uur × €${formData.uurtarief} = €${berekening.arbeidskosten.toFixed(2)}\nExtra kosten: €${berekening.extraKosten.toFixed(2)}\nSubtotaal: €${berekening.subtotaal.toFixed(2)}\n${berekening.kortingBedrag > 0 ? `Korting (${formData.kortingPercentage}%): -€${berekening.kortingBedrag.toFixed(2)}\\n` : ''}\nBTW (21%): €${berekening.btw21.toFixed(2)}\n\nTOTAAL: €${berekening.totaalInclBtw.toFixed(2)}\n    `.trim();\n\n    switch (method) {\n      case 'whatsapp':\n        window.open(`https://wa.me/${selectedKlant.telefoonnummer.replace(/\\D/g, '')}?text=${encodeURIComponent(message)}`, '_blank');\n        break;\n      case 'sms':\n        window.open(`sms:${selectedKlant.telefoonnummer}?body=${encodeURIComponent(message)}`, '_blank');\n        break;\n      case 'email':\n        window.open(`mailto:${selectedKlant.email}?subject=${encodeURIComponent('Kostenberekening')}&body=${encodeURIComponent(message)}`, '_blank');\n        break;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Snelle Berekening</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Bereken snel de kosten voor een klus\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Formulier */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-foreground mb-4\">Klus Details</h2>\n          \n          <div className=\"space-y-4\">\n            {/* Klant Selectie */}\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Klant *\n              </label>\n              <select\n                value={selectedKlantId}\n                onChange={(e) => setSelectedKlantId(e.target.value)}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"\">Selecteer een klant...</option>\n                {klanten.map((klant) => (\n                  <option key={klant.id} value={klant.id}>\n                    {klant.type === 'bedrijf' && klant.bedrijfsnaam \n                      ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`\n                      : `${klant.voornaam} ${klant.achternaam}`\n                    }\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Service Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Service Type\n              </label>\n              <select\n                name=\"serviceType\"\n                value={formData.serviceType}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                {serviceTypes.map((service) => (\n                  <option key={service.value} value={service.value}>\n                    {service.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Custom Service */}\n            {formData.serviceType === 'custom' && (\n              <div>\n                <label className=\"block text-sm font-medium text-foreground mb-2\">\n                  Aangepaste Service\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"customService\"\n                  value={formData.customService}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                  placeholder=\"Beschrijf de service...\"\n                />\n              </div>\n            )}\n\n            {/* Klusbeschrijving */}\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Klusbeschrijving\n              </label>\n              <textarea\n                name=\"klusbeschrijving\"\n                value={formData.klusbeschrijving}\n                onChange={handleInputChange}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                placeholder=\"Beschrijf de uitgevoerde werkzaamheden...\"\n              />\n            </div>\n\n            {/* Tijd en Tarief */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-foreground mb-2\">\n                  Uren\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"uren\"\n                  value={formData.uren}\n                  onChange={handleInputChange}\n                  min=\"0\"\n                  className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-foreground mb-2\">\n                  Minuten\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"minuten\"\n                  value={formData.minuten}\n                  onChange={handleInputChange}\n                  min=\"0\"\n                  max=\"59\"\n                  className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Uurtarief (€)\n              </label>\n              <input\n                type=\"number\"\n                name=\"uurtarief\"\n                value={formData.uurtarief}\n                onChange={handleInputChange}\n                step=\"0.01\"\n                min=\"0\"\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n\n            {/* Extra Kosten */}\n            <div className=\"space-y-3\">\n              <h3 className=\"text-md font-semibold text-foreground\">Extra Kosten</h3>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Voorrijkosten (€)\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"voorrijkosten\"\n                    value={formData.voorrijkosten}\n                    onChange={handleInputChange}\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Spoedservice (€)\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"spoedservice\"\n                    value={formData.spoedservice}\n                    onChange={handleInputChange}\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Parkeerkosten (€)\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"parkeerkosten\"\n                    value={formData.parkeerkosten}\n                    onChange={handleInputChange}\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Materiaalkosten (€)\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"materiaalkosten\"\n                    value={formData.materiaalkosten}\n                    onChange={handleInputChange}\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                  />\n                </div>\n              </div>\n\n              {formData.materiaalkosten > 0 && (\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Materiaal Details\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"materiaalDetails\"\n                    value={formData.materiaalDetails}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                    placeholder=\"Beschrijf het gebruikte materiaal...\"\n                  />\n                </div>\n              )}\n            </div>\n\n            {/* Korting */}\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Korting (%)\n              </label>\n              <select\n                name=\"kortingPercentage\"\n                value={formData.kortingPercentage}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value={0}>Geen korting</option>\n                <option value={5}>5%</option>\n                <option value={10}>10%</option>\n                <option value={15}>15%</option>\n                <option value={20}>20%</option>\n                <option value={25}>25%</option>\n              </select>\n            </div>\n\n            {/* Berekenen Button */}\n            <button\n              onClick={handleBerekenen}\n              className=\"w-full flex items-center justify-center space-x-2 bg-primary text-primary-foreground px-4 py-3 rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              <Calculator className=\"h-5 w-5\" />\n              <span>Berekenen</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Resultaat */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-foreground mb-4\">Berekening</h2>\n          \n          {berekening ? (\n            <div className=\"space-y-4\">\n              {/* Klant Info */}\n              {selectedKlant && (\n                <div className=\"p-4 bg-muted rounded-lg\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    {selectedKlant.type === 'bedrijf' ? (\n                      <Building className=\"h-4 w-4 text-green-600\" />\n                    ) : (\n                      <User className=\"h-4 w-4 text-blue-600\" />\n                    )}\n                    <span className=\"font-medium\">\n                      {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam \n                        ? selectedKlant.bedrijfsnaam \n                        : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`\n                      }\n                    </span>\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">\n                    {selectedKlant.adres} {selectedKlant.huisnummer}, {selectedKlant.postcode} {selectedKlant.stad}\n                  </div>\n                </div>\n              )}\n\n              {/* Berekening Details */}\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span>Arbeidskosten ({totalUren.toFixed(2)} uur × €{formData.uurtarief}):</span>\n                  <span>€{berekening.arbeidskosten.toFixed(2)}</span>\n                </div>\n                \n                {berekening.extraKosten > 0 && (\n                  <div className=\"flex justify-between\">\n                    <span>Extra kosten:</span>\n                    <span>€{berekening.extraKosten.toFixed(2)}</span>\n                  </div>\n                )}\n                \n                <div className=\"flex justify-between font-medium border-t border-border pt-2\">\n                  <span>Subtotaal:</span>\n                  <span>€{berekening.subtotaal.toFixed(2)}</span>\n                </div>\n                \n                {berekening.kortingBedrag > 0 && (\n                  <div className=\"flex justify-between text-green-600\">\n                    <span>Korting ({formData.kortingPercentage}%):</span>\n                    <span>-€{berekening.kortingBedrag.toFixed(2)}</span>\n                  </div>\n                )}\n                \n                <div className=\"flex justify-between\">\n                  <span>BTW (21%):</span>\n                  <span>€{berekening.btw21.toFixed(2)}</span>\n                </div>\n                \n                <div className=\"flex justify-between text-xl font-bold border-t border-border pt-3\">\n                  <span>TOTAAL INCL. BTW:</span>\n                  <span className=\"text-green-600\">€{berekening.totaalInclBtw.toFixed(2)}</span>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"space-y-3 pt-4 border-t border-border\">\n                <button\n                  onClick={handlePreview}\n                  className=\"w-full flex items-center justify-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors\"\n                >\n                  <Eye className=\"h-4 w-4\" />\n                  <span>Preview</span>\n                </button>\n\n                <div className=\"grid grid-cols-3 gap-2\">\n                  <button\n                    onClick={() => handleShare('whatsapp')}\n                    className=\"flex items-center justify-center space-x-1 bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm\"\n                  >\n                    <Share2 className=\"h-3 w-3\" />\n                    <span>WhatsApp</span>\n                  </button>\n                  <button\n                    onClick={() => handleShare('sms')}\n                    className=\"flex items-center justify-center space-x-1 bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm\"\n                  >\n                    <Share2 className=\"h-3 w-3\" />\n                    <span>SMS</span>\n                  </button>\n                  <button\n                    onClick={() => handleShare('email')}\n                    className=\"flex items-center justify-center space-x-1 bg-purple-500 text-white px-3 py-2 rounded-lg hover:bg-purple-600 transition-colors text-sm\"\n                  >\n                    <Share2 className=\"h-3 w-3\" />\n                    <span>E-mail</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <Calculator className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <p className=\"text-muted-foreground\">\n                Vul de gegevens in en klik op \"Berekenen\" om de kosten te zien\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Preview Modal */}\n      {showPreview && berekening && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold\">Kostenberekening Preview</h3>\n                <button\n                  onClick={() => setShowPreview(false)}\n                  className=\"text-muted-foreground hover:text-foreground\"\n                >\n                  ✕\n                </button>\n              </div>\n              \n              {/* Preview Content */}\n              <div className=\"space-y-4 text-sm\">\n                <div className=\"text-center\">\n                  <h2 className=\"text-lg font-bold\">Kostenberekening</h2>\n                  <p className=\"text-muted-foreground\">\n                    {new Date().toLocaleDateString('nl-NL')}\n                  </p>\n                </div>\n\n                {selectedKlant && (\n                  <div>\n                    <h3 className=\"font-semibold mb-2\">Klantgegevens:</h3>\n                    <p>{selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam \n                        ? selectedKlant.bedrijfsnaam \n                        : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`}</p>\n                    <p>{selectedKlant.adres} {selectedKlant.huisnummer}</p>\n                    <p>{selectedKlant.postcode} {selectedKlant.stad}</p>\n                  </div>\n                )}\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Werkzaamheden:</h3>\n                  <p><strong>Service:</strong> {formData.serviceType === 'custom' ? formData.customService : serviceTypes.find(s => s.value === formData.serviceType)?.label}</p>\n                  <p><strong>Beschrijving:</strong> {formData.klusbeschrijving}</p>\n                  <p><strong>Tijd:</strong> {totalUren.toFixed(2)} uur</p>\n                  <p><strong>Uurtarief:</strong> €{formData.uurtarief}</p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Kostenopbouw:</h3>\n                  <div className=\"space-y-1\">\n                    <div className=\"flex justify-between\">\n                      <span>Arbeidskosten:</span>\n                      <span>€{berekening.arbeidskosten.toFixed(2)}</span>\n                    </div>\n                    {berekening.extraKosten > 0 && (\n                      <div className=\"flex justify-between\">\n                        <span>Extra kosten:</span>\n                        <span>€{berekening.extraKosten.toFixed(2)}</span>\n                      </div>\n                    )}\n                    <div className=\"flex justify-between font-medium border-t pt-1\">\n                      <span>Subtotaal:</span>\n                      <span>€{berekening.subtotaal.toFixed(2)}</span>\n                    </div>\n                    {berekening.kortingBedrag > 0 && (\n                      <div className=\"flex justify-between text-green-600\">\n                        <span>Korting ({formData.kortingPercentage}%):</span>\n                        <span>-€{berekening.kortingBedrag.toFixed(2)}</span>\n                      </div>\n                    )}\n                    <div className=\"flex justify-between\">\n                      <span>BTW (21%):</span>\n                      <span>€{berekening.btw21.toFixed(2)}</span>\n                    </div>\n                    <div className=\"flex justify-between text-lg font-bold border-t pt-2\">\n                      <span>TOTAAL INCL. BTW:</span>\n                      <span>€{berekening.totaalInclBtw.toFixed(2)}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-2 mt-6 pt-4 border-t\">\n                <button\n                  onClick={() => setShowPreview(false)}\n                  className=\"px-4 py-2 text-muted-foreground hover:text-foreground transition-colors\"\n                >\n                  Sluiten\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAee,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAE/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,WAAW,qBAAqB,kBAAkB;QAClD,MAAM;QACN,SAAS;QACT,iBAAiB,qBAAqB,cAAc;QACpD,gBAAgB,qBAAqB,aAAa;QAClD,eAAe,qBAAqB,sBAAsB;QAC1D,cAAc,qBAAqB,qBAAqB;QACxD,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAU,OAAO;QAAY;KACvC;IAED,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD,MAAM,YAAY,SAAS,IAAI,GAAI,SAAS,OAAO,GAAG;IAEtD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,WAAW,UAAU,IAAI;YACvD,CAAC;IACH;IAEA,MAAM,gBAAgB;QACpB,MAAM,gBAAgB,YAAY,SAAS,SAAS;QACpD,MAAM,cAAc,SAAS,aAAa,GAAG,SAAS,YAAY,GAAG,SAAS,aAAa,GAAG,SAAS,eAAe;QACtH,MAAM,YAAY,gBAAgB;QAElC,MAAM,gBAAgB,AAAC,YAAY,SAAS,iBAAiB,GAAI;QACjE,MAAM,qBAAqB,YAAY;QAEvC,0CAA0C;QAC1C,MAAM,QAAQ,AAAC,qBAAqB,SAAS,eAAe,GAAI;QAChE,MAAM,OAAO,GAAG,mDAAmD;QAEnE,MAAM,gBAAgB,qBAAqB,QAAQ;QAEnD,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,OAAO;YACP,UAAU;gBAAE,GAAG,QAAQ;gBAAE;YAAU;QACrC;QAEA,cAAc;QACd,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,iBAAiB;YACpB,MAAM;YACN;QACF;QACA,IAAI,cAAc,GAAG;YACnB,MAAM;YACN;QACF;QACA;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY;YACd,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,eAAe;QAEnC,MAAM,UAAU,CAAC;sBACC,EAAE,cAAc,QAAQ,CAAC,CAAC,EAAE,cAAc,UAAU,CAAC;;SAElE,EAAE,SAAS,WAAW,KAAK,WAAW,SAAS,aAAa,GAAG,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,WAAW,GAAG,MAAM;cAC1H,EAAE,SAAS,gBAAgB,CAAC;;eAE3B,EAAE,UAAU,OAAO,CAAC,GAAG,QAAQ,EAAE,SAAS,SAAS,CAAC,IAAI,EAAE,WAAW,aAAa,CAAC,OAAO,CAAC,GAAG;eAC9F,EAAE,WAAW,WAAW,CAAC,OAAO,CAAC,GAAG;YACvC,EAAE,WAAW,SAAS,CAAC,OAAO,CAAC,GAAG;AAC9C,EAAE,WAAW,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,SAAS,iBAAiB,CAAC,MAAM,EAAE,WAAW,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG;YACjH,EAAE,WAAW,KAAK,CAAC,OAAO,CAAC,GAAG;;SAEjC,EAAE,WAAW,aAAa,CAAC,OAAO,CAAC,GAAG;IAC3C,CAAC,CAAC,IAAI;QAEN,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBACpH;YACF,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,cAAc,cAAc,CAAC,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBACvF;YACF,KAAK;gBACH,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,KAAK,CAAC,SAAS,EAAE,mBAAmB,oBAAoB,MAAM,EAAE,mBAAmB,UAAU,EAAE;gBACnI;QACJ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAE3D,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC;4DAAsB,OAAO,MAAM,EAAE;sEACnC,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC3C,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,GAC/D,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE;2DAHhC,MAAM,EAAE;;;;;;;;;;;;;;;;;kDAW3B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,WAAU;0DAET,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;wDAA2B,OAAO,QAAQ,KAAK;kEAC7C,QAAQ,KAAK;uDADH,QAAQ,KAAK;;;;;;;;;;;;;;;;oCAQ/B,SAAS,WAAW,KAAK,0BACxB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAMlB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,gBAAgB;gDAChC,UAAU;gDACV,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAGlE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,KAAI;wDACJ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAGlE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,SAAS;gDACzB,UAAU;gDACV,MAAK;gDACL,KAAI;gDACJ,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,aAAa;gEAC7B,UAAU;gEACV,MAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,MAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,aAAa;gEAC7B,UAAU;gEACV,MAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,eAAe;gEAC/B,UAAU;gEACV,MAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;;;;;;;;;;;;;4CAKf,SAAS,eAAe,GAAG,mBAC1B,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAGlE,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,gBAAgB;wDAChC,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAOpB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU;gDACV,WAAU;;kEAEV,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;kDAKvB,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;4BAE1D,2BACC,6LAAC;gCAAI,WAAU;;oCAEZ,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,IAAI,KAAK,0BACtB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;6EAEpB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAElB,6LAAC;wDAAK,WAAU;kEACb,cAAc,IAAI,KAAK,aAAa,cAAc,YAAY,GAC3D,cAAc,YAAY,GAC1B,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAE,cAAc,UAAU,EAAE;;;;;;;;;;;;0DAI/D,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,KAAK;oDAAC;oDAAE,cAAc,UAAU;oDAAC;oDAAG,cAAc,QAAQ;oDAAC;oDAAE,cAAc,IAAI;;;;;;;;;;;;;kDAMpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAgB,UAAU,OAAO,CAAC;4DAAG;4DAAS,SAAS,SAAS;4DAAC;;;;;;;kEACvE,6LAAC;;4DAAK;4DAAE,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAG1C,WAAW,WAAW,GAAG,mBACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAK;4DAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAI3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAK;4DAAE,WAAW,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAGtC,WAAW,aAAa,GAAG,mBAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAU,SAAS,iBAAiB;4DAAC;;;;;;;kEAC3C,6LAAC;;4DAAK;4DAAG,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAI9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAK;4DAAE,WAAW,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAGnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;;4DAAiB;4DAAE,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAK;;;;;;;;;;;;0DAGR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDACC,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAMd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;YAS5C,eAAe,4BACd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DACV,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;oCAIlC,+BACC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;0DAAG,cAAc,IAAI,KAAK,aAAa,cAAc,YAAY,GAC5D,cAAc,YAAY,GAC1B,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAE,cAAc,UAAU,EAAE;;;;;;0DAC7D,6LAAC;;oDAAG,cAAc,KAAK;oDAAC;oDAAE,cAAc,UAAU;;;;;;;0DAClD,6LAAC;;oDAAG,cAAc,QAAQ;oDAAC;oDAAE,cAAc,IAAI;;;;;;;;;;;;;kDAInD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAiB;oDAAE,SAAS,WAAW,KAAK,WAAW,SAAS,aAAa,GAAG,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,WAAW,GAAG;;;;;;;0DACrJ,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAsB;oDAAE,SAAS,gBAAgB;;;;;;;0DAC5D,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;oDAAE,UAAU,OAAO,CAAC;oDAAG;;;;;;;0DAChD,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAmB;oDAAG,SAAS,SAAS;;;;;;;;;;;;;kDAGrD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;oDAE1C,WAAW,WAAW,GAAG,mBACxB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;kEAG3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,WAAW,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;oDAEtC,WAAW,aAAa,GAAG,mBAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAU,SAAS,iBAAiB;oEAAC;;;;;;;0EAC3C,6LAAC;;oEAAK;oEAAG,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;kEAG9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,WAAW,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;kEAEnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAlkBwB;;QACoB,iIAAA,CAAA,SAAM;;;KAD1B"}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}