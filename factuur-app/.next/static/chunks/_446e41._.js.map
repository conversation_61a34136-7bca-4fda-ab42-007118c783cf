{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/facturen/nieuw/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { useApp } from '@/contexts/AppContext';\nimport {\n  ArrowLeft,\n  Save,\n  Plus,\n  Trash2,\n  FileText,\n  QrCode,\n  Calculator\n} from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function NieuweFactuurPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const {\n    klanten,\n    getKlant,\n    createFactuur,\n    generateFactuurnummer,\n    bedrijfsInstellingen\n  } = useApp();\n\n  // Get URL parameters for pre-filling\n  const klantIdParam = searchParams.get('klantId');\n  const tijdregistratieIdParam = searchParams.get('tijdregistratieId');\n  const urenParam = searchParams.get('uren');\n\n  const [formData, setFormData] = useState({\n    klantId: klantIdParam || '',\n    factuurdatum: new Date().toISOString().split('T')[0],\n    vervaldatum: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    uitvoerdatum: new Date().toISOString().split('T')[0],\n    serviceType: 'loodgieter' as const,\n    customService: '',\n    betaaltermijn: bedrijfsInstellingen.standaardBetaaltermijn,\n    qrCode: '',\n    betaalLink: '',\n  });\n\n  const [factuurregels, setFactuurregels] = useState([\n    {\n      id: '1',\n      aantal: 1,\n      beschrijving: 'Werkzaamheden',\n      detailBeschrijving: '',\n      eenheid: 'uur',\n      uren: parseFloat(urenParam || '0'),\n      prijsPerUur: bedrijfsInstellingen.standaardUurtarief,\n      totaalExclBtw: 0,\n    }\n  ]);\n\n  const [extraKosten, setExtraKosten] = useState({\n    voorrijkosten: bedrijfsInstellingen.standaardVoorrijkosten,\n    spoedservice: bedrijfsInstellingen.standaardSpoedservice,\n    parkeerkosten: 0,\n    materiaalkosten: 0,\n    materiaalDetails: '',\n  });\n\n  const [kortingPercentage, setKortingPercentage] = useState(0);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const serviceTypes = [\n    { value: 'loodgieter', label: 'Loodgieter' },\n    { value: 'elektricien', label: 'Elektricien' },\n    { value: 'klusjesman', label: 'Klusjesman' },\n    { value: 'timmerman', label: 'Timmerman' },\n    { value: 'aannemer', label: 'Aannemer' },\n    { value: 'custom', label: 'Anders...' },\n  ];\n\n  // Calculate totals\n  const subtotaal = factuurregels.reduce((sum, regel) => sum + regel.totaalExclBtw, 0) +\n                   Object.values(extraKosten).reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0);\n  const kortingBedrag = (subtotaal * kortingPercentage) / 100;\n  const subtotaalNaKorting = subtotaal - kortingBedrag;\n  const btw21 = (subtotaalNaKorting * bedrijfsInstellingen.standaardBtw21) / 100;\n  const btw9 = 0; // Voor eenvoud\n  const totaalInclBtw = subtotaalNaKorting + btw21 + btw9;\n\n  // Update factuurregels when values change\n  useEffect(() => {\n    setFactuurregels(prev => prev.map(regel => ({\n      ...regel,\n      totaalExclBtw: regel.aantal * regel.uren * regel.prijsPerUur\n    })));\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || 0 : value\n    }));\n  };\n\n  const handleFactuurregelChange = (id: string, field: string, value: any) => {\n    setFactuurregels(prev => prev.map(regel => {\n      if (regel.id === id) {\n        const updated = { ...regel, [field]: value };\n        updated.totaalExclBtw = updated.aantal * updated.uren * updated.prijsPerUur;\n        return updated;\n      }\n      return regel;\n    }));\n  };\n\n  const addFactuurregel = () => {\n    const newId = (factuurregels.length + 1).toString();\n    setFactuurregels(prev => [...prev, {\n      id: newId,\n      aantal: 1,\n      beschrijving: '',\n      detailBeschrijving: '',\n      eenheid: 'uur',\n      uren: 0,\n      prijsPerUur: bedrijfsInstellingen.standaardUurtarief,\n      totaalExclBtw: 0,\n    }]);\n  };\n\n  const removeFactuurregel = (id: string) => {\n    if (factuurregels.length > 1) {\n      setFactuurregels(prev => prev.filter(regel => regel.id !== id));\n    }\n  };\n\n  const handleExtraKostenChange = (field: string, value: number | string) => {\n    setExtraKosten(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!formData.klantId) {\n      alert('Selecteer een klant');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const factuurData = {\n        klantId: formData.klantId,\n        factuurdatum: new Date(formData.factuurdatum),\n        vervaldatum: new Date(formData.vervaldatum),\n        uitvoerdatum: new Date(formData.uitvoerdatum),\n        serviceType: formData.serviceType,\n        customService: formData.customService,\n        betaaltermijn: formData.betaaltermijn,\n        factuurregels,\n        extraKosten,\n        kortingPercentage,\n        kortingBedrag,\n        subtotaal,\n        btw21,\n        btw9,\n        totaalInclBtw,\n        qrCode: formData.qrCode,\n        betaalLink: formData.betaalLink,\n        status: 'concept' as const,\n        tijdregistratieId: tijdregistratieIdParam,\n      };\n\n      const factuurId = createFactuur(factuurData);\n      router.push(`/facturen/${factuurId}`);\n    } catch (error) {\n      console.error('Error creating factuur:', error);\n      alert('Er is een fout opgetreden bij het maken van de factuur');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const selectedKlant = getKlant(formData.klantId);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          href=\"/facturen\"\n          className=\"p-2 hover:bg-accent rounded-lg transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n        </Link>\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Nieuwe Factuur</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Maak een professionele factuur\n          </p>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Klant en Factuurgegevens */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-foreground mb-4\">Factuurgegevens</h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Klant *\n              </label>\n              <select\n                name=\"klantId\"\n                value={formData.klantId}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"\">Selecteer een klant...</option>\n                {klanten.map((klant) => (\n                  <option key={klant.id} value={klant.id}>\n                    {klant.type === 'bedrijf' && klant.bedrijfsnaam\n                      ? `${klant.bedrijfsnaam} (${klant.voornaam} ${klant.achternaam})`\n                      : `${klant.voornaam} ${klant.achternaam}`\n                    }\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Factuurnummer\n              </label>\n              <input\n                type=\"text\"\n                value={generateFactuurnummer()}\n                disabled\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-muted text-muted-foreground\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Factuurdatum *\n              </label>\n              <input\n                type=\"date\"\n                name=\"factuurdatum\"\n                value={formData.factuurdatum}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Vervaldatum *\n              </label>\n              <input\n                type=\"date\"\n                name=\"vervaldatum\"\n                value={formData.vervaldatum}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Uitvoerdatum *\n              </label>\n              <input\n                type=\"date\"\n                name=\"uitvoerdatum\"\n                value={formData.uitvoerdatum}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Service Type\n              </label>\n              <select\n                name=\"serviceType\"\n                value={formData.serviceType}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                {serviceTypes.map((service) => (\n                  <option key={service.value} value={service.value}>\n                    {service.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {formData.serviceType === 'custom' && (\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Aangepaste Service\n              </label>\n              <input\n                type=\"text\"\n                name=\"customService\"\n                value={formData.customService}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                placeholder=\"Beschrijf de service...\"\n              />\n            </div>\n          )}\n\n          <div className=\"mt-4\">\n            <label className=\"block text-sm font-medium text-foreground mb-2\">\n              Betalingstermijn (dagen)\n            </label>\n            <select\n              name=\"betaaltermijn\"\n              value={formData.betaaltermijn}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n            >\n              <option value={7}>7 dagen</option>\n              <option value={14}>14 dagen</option>\n              <option value={30}>30 dagen</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Klantgegevens Preview */}\n        {selectedKlant && (\n          <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n            <h3 className=\"text-lg font-semibold text-foreground mb-4\">Klantgegevens</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium\">Naam:</span>\n                <div className=\"mt-1\">\n                  {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam\n                    ? selectedKlant.bedrijfsnaam\n                    : `${selectedKlant.voornaam} ${selectedKlant.achternaam}`\n                  }\n                </div>\n                {selectedKlant.type === 'bedrijf' && selectedKlant.bedrijfsnaam && (\n                  <div className=\"text-muted-foreground\">\n                    Contactpersoon: {selectedKlant.voornaam} {selectedKlant.achternaam}\n                  </div>\n                )}\n              </div>\n              <div>\n                <span className=\"font-medium\">Adres:</span>\n                <div className=\"mt-1\">\n                  {selectedKlant.adres} {selectedKlant.huisnummer}<br />\n                  {selectedKlant.postcode} {selectedKlant.stad}\n                </div>\n              </div>\n              <div>\n                <span className=\"font-medium\">Telefoon:</span>\n                <div className=\"mt-1\">{selectedKlant.telefoonnummer}</div>\n              </div>\n              <div>\n                <span className=\"font-medium\">E-mail:</span>\n                <div className=\"mt-1\">{selectedKlant.email}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Factuurregels */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold text-foreground\">Factuurregels</h2>\n            <button\n              type=\"button\"\n              onClick={addFactuurregel}\n              className=\"flex items-center space-x-2 bg-secondary text-secondary-foreground px-3 py-2 rounded-lg hover:bg-secondary/90 transition-colors\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              <span>Regel Toevoegen</span>\n            </button>\n          </div>\n\n          <div className=\"space-y-4\">\n            {factuurregels.map((regel, index) => (\n              <div key={regel.id} className=\"border border-border rounded-lg p-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-foreground mb-1\">\n                      Aantal\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={regel.aantal}\n                      onChange={(e) => handleFactuurregelChange(regel.id, 'aantal', parseFloat(e.target.value) || 0)}\n                      min=\"0\"\n                      step=\"0.01\"\n                      className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                    />\n                  </div>\n\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-foreground mb-1\">\n                      Beschrijving\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={regel.beschrijving}\n                      onChange={(e) => handleFactuurregelChange(regel.id, 'beschrijving', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                      placeholder=\"Beschrijving van werkzaamheden\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-foreground mb-1\">\n                      Uren\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={regel.uren}\n                      onChange={(e) => handleFactuurregelChange(regel.id, 'uren', parseFloat(e.target.value) || 0)}\n                      min=\"0\"\n                      step=\"0.01\"\n                      className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-foreground mb-1\">\n                      Prijs/uur (€)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={regel.prijsPerUur}\n                      onChange={(e) => handleFactuurregelChange(regel.id, 'prijsPerUur', parseFloat(e.target.value) || 0)}\n                      min=\"0\"\n                      step=\"0.01\"\n                      className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                    />\n                  </div>\n\n                  <div className=\"flex items-end\">\n                    <div className=\"flex-1\">\n                      <label className=\"block text-sm font-medium text-foreground mb-1\">\n                        Totaal (€)\n                      </label>\n                      <div className=\"px-3 py-2 border border-input rounded-lg bg-muted text-foreground font-medium\">\n                        €{regel.totaalExclBtw.toFixed(2)}\n                      </div>\n                    </div>\n                    {factuurregels.length > 1 && (\n                      <button\n                        type=\"button\"\n                        onClick={() => removeFactuurregel(regel.id)}\n                        className=\"ml-2 p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"mt-3\">\n                  <label className=\"block text-sm font-medium text-foreground mb-1\">\n                    Detail Beschrijving (optioneel)\n                  </label>\n                  <textarea\n                    value={regel.detailBeschrijving}\n                    onChange={(e) => handleFactuurregelChange(regel.id, 'detailBeschrijving', e.target.value)}\n                    rows={2}\n                    className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                    placeholder=\"Gedetailleerde beschrijving van de werkzaamheden...\"\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Extra Kosten */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-foreground mb-4\">Extra Kosten</h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Voorrijkosten (€)\n              </label>\n              <select\n                value={extraKosten.voorrijkosten}\n                onChange={(e) => handleExtraKostenChange('voorrijkosten', parseFloat(e.target.value))}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value={0}>€0 - Geen voorrijkosten</option>\n                <option value={25}>€25 - Standaard voorrijkosten</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Spoedservice (€)\n              </label>\n              <select\n                value={extraKosten.spoedservice}\n                onChange={(e) => handleExtraKostenChange('spoedservice', parseFloat(e.target.value))}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value={0}>€0 - Geen spoedservice</option>\n                <option value={25}>€25 - Spoedservice toeslag</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Parkeerkosten (€)\n              </label>\n              <input\n                type=\"number\"\n                value={extraKosten.parkeerkosten}\n                onChange={(e) => handleExtraKostenChange('parkeerkosten', parseFloat(e.target.value) || 0)}\n                min=\"0\"\n                step=\"0.01\"\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Materiaalkosten (€)\n              </label>\n              <input\n                type=\"number\"\n                value={extraKosten.materiaalkosten}\n                onChange={(e) => handleExtraKostenChange('materiaalkosten', parseFloat(e.target.value) || 0)}\n                min=\"0\"\n                step=\"0.01\"\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              />\n            </div>\n          </div>\n\n          {extraKosten.materiaalkosten > 0 && (\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Materiaal Details\n              </label>\n              <textarea\n                value={extraKosten.materiaalDetails}\n                onChange={(e) => handleExtraKostenChange('materiaalDetails', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                placeholder=\"Beschrijf het gebruikte materiaal...\"\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Korting en QR Code */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n            <h3 className=\"text-lg font-semibold text-foreground mb-4\">Korting</h3>\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Korting (%)\n              </label>\n              <select\n                value={kortingPercentage}\n                onChange={(e) => setKortingPercentage(parseFloat(e.target.value))}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value={0}>Geen korting</option>\n                <option value={5}>5%</option>\n                <option value={10}>10%</option>\n                <option value={15}>15%</option>\n                <option value={20}>20%</option>\n                <option value={25}>25%</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n            <h3 className=\"text-lg font-semibold text-foreground mb-4\">Betaling</h3>\n            <div>\n              <label className=\"block text-sm font-medium text-foreground mb-2\">\n                Betaal Link (optioneel)\n              </label>\n              <input\n                type=\"url\"\n                name=\"betaalLink\"\n                value={formData.betaalLink}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                placeholder=\"https://betaal.link/...\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Totaal Overzicht */}\n        <div className=\"bg-card border border-border rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-foreground mb-4\">Totaal Overzicht</h2>\n\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span>Subtotaal:</span>\n              <span>€{subtotaal.toFixed(2)}</span>\n            </div>\n\n            {kortingBedrag > 0 && (\n              <div className=\"flex justify-between text-green-600\">\n                <span>Korting ({kortingPercentage}%):</span>\n                <span>-€{kortingBedrag.toFixed(2)}</span>\n              </div>\n            )}\n\n            <div className=\"flex justify-between\">\n              <span>BTW (21%):</span>\n              <span>€{btw21.toFixed(2)}</span>\n            </div>\n\n            <div className=\"flex justify-between text-xl font-bold border-t border-border pt-3\">\n              <span>TOTAAL INCL. BTW:</span>\n              <span className=\"text-green-600\">€{totaalInclBtw.toFixed(2)}</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-4\">\n          <Link\n            href=\"/facturen\"\n            className=\"px-4 py-2 text-muted-foreground hover:text-foreground transition-colors\"\n          >\n            Annuleren\n          </Link>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <Save className=\"h-4 w-4\" />\n            <span>{isSubmitting ? 'Opslaan...' : 'Factuur Maken'}</span>\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAUA;AATA;AAAA;AAAA;AAAA;;;AALA;;;;;;AAgBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,aAAa,EACb,qBAAqB,EACrB,oBAAoB,EACrB,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAET,qCAAqC;IACrC,MAAM,eAAe,aAAa,GAAG,CAAC;IACtC,MAAM,yBAAyB,aAAa,GAAG,CAAC;IAChD,MAAM,YAAY,aAAa,GAAG,CAAC;IAEnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS,gBAAgB;QACzB,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACvF,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,aAAa;QACb,eAAe;QACf,eAAe,qBAAqB,sBAAsB;QAC1D,QAAQ;QACR,YAAY;IACd;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD;YACE,IAAI;YACJ,QAAQ;YACR,cAAc;YACd,oBAAoB;YACpB,SAAS;YACT,MAAM,WAAW,aAAa;YAC9B,aAAa,qBAAqB,kBAAkB;YACpD,eAAe;QACjB;KACD;IAED,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,eAAe,qBAAqB,sBAAsB;QAC1D,cAAc,qBAAqB,qBAAqB;QACxD,eAAe;QACf,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAU,OAAO;QAAY;KACvC;IAED,mBAAmB;IACnB,MAAM,YAAY,cAAc,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,aAAa,EAAE,KACjE,OAAO,MAAM,CAAC,aAAa,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,OAAO,QAAQ,WAAW,MAAM,CAAC,GAAG;IAC5G,MAAM,gBAAgB,AAAC,YAAY,oBAAqB;IACxD,MAAM,qBAAqB,YAAY;IACvC,MAAM,QAAQ,AAAC,qBAAqB,qBAAqB,cAAc,GAAI;IAC3E,MAAM,OAAO,GAAG,eAAe;IAC/B,MAAM,gBAAgB,qBAAqB,QAAQ;IAEnD,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;+CAAiB,CAAA,OAAQ,KAAK,GAAG;uDAAC,CAAA,QAAS,CAAC;gCAC1C,GAAG,KAAK;gCACR,eAAe,MAAM,MAAM,GAAG,MAAM,IAAI,GAAG,MAAM,WAAW;4BAC9D,CAAC;;;QACH;sCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,WAAW,UAAU,IAAI;YACvD,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC,IAAY,OAAe;QAC3D,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAChC,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,UAAU;wBAAE,GAAG,KAAK;wBAAE,CAAC,MAAM,EAAE;oBAAM;oBAC3C,QAAQ,aAAa,GAAG,QAAQ,MAAM,GAAG,QAAQ,IAAI,GAAG,QAAQ,WAAW;oBAC3E,OAAO;gBACT;gBACA,OAAO;YACT;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ,CAAC,cAAc,MAAM,GAAG,CAAC,EAAE,QAAQ;QACjD,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;oBACjC,IAAI;oBACJ,QAAQ;oBACR,cAAc;oBACd,oBAAoB;oBACpB,SAAS;oBACT,MAAM;oBACN,aAAa,qBAAqB,kBAAkB;oBACpD,eAAe;gBACjB;aAAE;IACJ;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC7D;IACF;IAEA,MAAM,0BAA0B,CAAC,OAAe;QAC9C,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACrD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,cAAc;gBAClB,SAAS,SAAS,OAAO;gBACzB,cAAc,IAAI,KAAK,SAAS,YAAY;gBAC5C,aAAa,IAAI,KAAK,SAAS,WAAW;gBAC1C,cAAc,IAAI,KAAK,SAAS,YAAY;gBAC5C,aAAa,SAAS,WAAW;gBACjC,eAAe,SAAS,aAAa;gBACrC,eAAe,SAAS,aAAa;gBACrC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,QAAQ;gBACR,mBAAmB;YACrB;YAEA,MAAM,YAAY,cAAc;YAChC,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB,SAAS,SAAS,OAAO;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAE3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,QAAQ;gDACR,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC;4DAAsB,OAAO,MAAM,EAAE;sEACnC,MAAM,IAAI,KAAK,aAAa,MAAM,YAAY,GAC3C,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,GAC/D,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,UAAU,EAAE;2DAHhC,MAAM,EAAE;;;;;;;;;;;;;;;;;kDAU3B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU;gDACV,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU;gDACV,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,WAAU;0DAET,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;wDAA2B,OAAO,QAAQ,KAAK;kEAC7C,QAAQ,KAAK;uDADH,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;4BAQjC,SAAS,WAAW,KAAK,0BACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAGlE,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAGlE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,6LAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,6LAAC;gDAAO,OAAO;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;oBAMxB,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;0DACZ,cAAc,IAAI,KAAK,aAAa,cAAc,YAAY,GAC3D,cAAc,YAAY,GAC1B,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAE,cAAc,UAAU,EAAE;;;;;;4CAG5D,cAAc,IAAI,KAAK,aAAa,cAAc,YAAY,kBAC7D,6LAAC;gDAAI,WAAU;;oDAAwB;oDACpB,cAAc,QAAQ;oDAAC;oDAAE,cAAc,UAAU;;;;;;;;;;;;;kDAIxE,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,KAAK;oDAAC;oDAAE,cAAc,UAAU;kEAAC,6LAAC;;;;;oDAChD,cAAc,QAAQ;oDAAC;oDAAE,cAAc,IAAI;;;;;;;;;;;;;kDAGhD,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;0DAAQ,cAAc,cAAc;;;;;;;;;;;;kDAErD,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;0DAAQ,cAAc,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,OAAO,MAAM,MAAM;gEACnB,UAAU,CAAC,IAAM,yBAAyB,MAAM,EAAE,EAAE,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC5F,KAAI;gEACJ,MAAK;gEACL,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,OAAO,MAAM,YAAY;gEACzB,UAAU,CAAC,IAAM,yBAAyB,MAAM,EAAE,EAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAClF,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,OAAO,MAAM,IAAI;gEACjB,UAAU,CAAC,IAAM,yBAAyB,MAAM,EAAE,EAAE,QAAQ,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC1F,KAAI;gEACJ,MAAK;gEACL,WAAU;;;;;;;;;;;;kEAId,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAGlE,6LAAC;gEACC,MAAK;gEACL,OAAO,MAAM,WAAW;gEACxB,UAAU,CAAC,IAAM,yBAAyB,MAAM,EAAE,EAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACjG,KAAI;gEACJ,MAAK;gEACL,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,WAAU;kFAAiD;;;;;;kFAGlE,6LAAC;wEAAI,WAAU;;4EAAgF;4EAC3F,MAAM,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;4DAGjC,cAAc,MAAM,GAAG,mBACtB,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,mBAAmB,MAAM,EAAE;gEAC1C,WAAU;0EAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAM1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAGlE,6LAAC;wDACC,OAAO,MAAM,kBAAkB;wDAC/B,UAAU,CAAC,IAAM,yBAAyB,MAAM,EAAE,EAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wDACxF,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;;uCAvFR,MAAM,EAAE;;;;;;;;;;;;;;;;kCAgGxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAE3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,OAAO,YAAY,aAAa;gDAChC,UAAU,CAAC,IAAM,wBAAwB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gDACnF,WAAU;;kEAEV,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;kDAIvB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,OAAO,YAAY,YAAY;gDAC/B,UAAU,CAAC,IAAM,wBAAwB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAClF,WAAU;;kEAEV,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;kDAIvB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,aAAa;gDAChC,UAAU,CAAC,IAAM,wBAAwB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDACxF,KAAI;gDACJ,MAAK;gDACL,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,eAAe;gDAClC,UAAU,CAAC,IAAM,wBAAwB,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC1F,KAAI;gDACJ,MAAK;gDACL,WAAU;;;;;;;;;;;;;;;;;;4BAKf,YAAY,eAAe,GAAG,mBAC7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAGlE,6LAAC;wCACC,OAAO,YAAY,gBAAgB;wCACnC,UAAU,CAAC,IAAM,wBAAwB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAC3E,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAOpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAU;;kEAEV,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAG;;;;;;kEAClB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,6LAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAKzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAGlE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAE3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAK;oDAAE,UAAU,OAAO,CAAC;;;;;;;;;;;;;oCAG3B,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAU;oDAAkB;;;;;;;0DAClC,6LAAC;;oDAAK;oDAAG,cAAc,OAAO,CAAC;;;;;;;;;;;;;kDAInC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAK;oDAAE,MAAM,OAAO,CAAC;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;;oDAAiB;oDAAE,cAAc,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAK/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GA3nBwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAOhC,iIAAA,CAAA,SAAM;;;KATY"}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1525, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1690, "column": 0}, "map": {"version": 3, "file": "save.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}