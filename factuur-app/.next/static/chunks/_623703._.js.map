{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/app/klanten/nieuw/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useApp } from '@/contexts/AppContext';\nimport { ArrowLeft, Save, User, Building } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function NieuweKlantPage() {\n  const router = useRouter();\n  const { addKlant } = useApp();\n  \n  const [formData, setFormData] = useState({\n    type: 'particulier' as 'particulier' | 'bedrijf',\n    voornaam: '',\n    achternaam: '',\n    bedrijfsnaam: '',\n    adres: '',\n    huisnummer: '',\n    postcode: '',\n    stad: '',\n    telefoonnummer: '',\n    email: '',\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.voornaam.trim()) {\n      newErrors.voornaam = 'Voornaam is verplicht';\n    }\n\n    if (!formData.achternaam.trim()) {\n      newErrors.achternaam = 'Achternaam is verplicht';\n    }\n\n    if (formData.type === 'bedrijf' && !formData.bedrijfsnaam.trim()) {\n      newErrors.bedrijfsnaam = 'Bedrijfsnaam is verplicht voor bedrijven';\n    }\n\n    if (!formData.adres.trim()) {\n      newErrors.adres = 'Adres is verplicht';\n    }\n\n    if (!formData.huisnummer.trim()) {\n      newErrors.huisnummer = 'Huisnummer is verplicht';\n    }\n\n    if (!formData.postcode.trim()) {\n      newErrors.postcode = 'Postcode is verplicht';\n    } else if (!/^\\d{4}\\s?[A-Za-z]{2}$/.test(formData.postcode)) {\n      newErrors.postcode = 'Ongeldige postcode (bijv. 1234 AB)';\n    }\n\n    if (!formData.stad.trim()) {\n      newErrors.stad = 'Stad is verplicht';\n    }\n\n    if (!formData.telefoonnummer.trim()) {\n      newErrors.telefoonnummer = 'Telefoonnummer is verplicht';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'E-mailadres is verplicht';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Ongeldig e-mailadres';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Format postcode\n      const formattedPostcode = formData.postcode.replace(/\\s/g, '').replace(/(\\d{4})([A-Za-z]{2})/, '$1 $2').toUpperCase();\n      \n      addKlant({\n        ...formData,\n        postcode: formattedPostcode,\n      });\n\n      // Redirect to klanten page\n      router.push('/klanten');\n    } catch (error) {\n      console.error('Error adding klant:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          href=\"/klanten\"\n          className=\"p-2 hover:bg-accent rounded-lg transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n        </Link>\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Nieuwe Klant</h1>\n          <p className=\"text-muted-foreground mt-1\">\n            Voeg een nieuwe klant toe aan uw systeem\n          </p>\n        </div>\n      </div>\n\n      {/* Form */}\n      <div className=\"bg-card border border-border rounded-lg shadow-sm\">\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Klant Type */}\n          <div>\n            <label className=\"block text-sm font-medium text-foreground mb-3\">\n              Type Klant\n            </label>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <button\n                type=\"button\"\n                onClick={() => setFormData(prev => ({ ...prev, type: 'particulier' }))}\n                className={`p-4 border rounded-lg transition-colors ${\n                  formData.type === 'particulier'\n                    ? 'border-primary bg-primary/10 text-primary'\n                    : 'border-border hover:bg-accent'\n                }`}\n              >\n                <User className=\"h-6 w-6 mx-auto mb-2\" />\n                <span className=\"block text-sm font-medium\">Particulier</span>\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setFormData(prev => ({ ...prev, type: 'bedrijf' }))}\n                className={`p-4 border rounded-lg transition-colors ${\n                  formData.type === 'bedrijf'\n                    ? 'border-primary bg-primary/10 text-primary'\n                    : 'border-border hover:bg-accent'\n                }`}\n              >\n                <Building className=\"h-6 w-6 mx-auto mb-2\" />\n                <span className=\"block text-sm font-medium\">Bedrijf</span>\n              </button>\n            </div>\n          </div>\n\n          {/* Personal Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"voornaam\" className=\"block text-sm font-medium text-foreground mb-2\">\n                Voornaam *\n              </label>\n              <input\n                type=\"text\"\n                id=\"voornaam\"\n                name=\"voornaam\"\n                value={formData.voornaam}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                  errors.voornaam ? 'border-destructive' : 'border-input'\n                }`}\n                placeholder=\"Voornaam\"\n              />\n              {errors.voornaam && (\n                <p className=\"text-destructive text-sm mt-1\">{errors.voornaam}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"achternaam\" className=\"block text-sm font-medium text-foreground mb-2\">\n                Achternaam *\n              </label>\n              <input\n                type=\"text\"\n                id=\"achternaam\"\n                name=\"achternaam\"\n                value={formData.achternaam}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                  errors.achternaam ? 'border-destructive' : 'border-input'\n                }`}\n                placeholder=\"Achternaam\"\n              />\n              {errors.achternaam && (\n                <p className=\"text-destructive text-sm mt-1\">{errors.achternaam}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Company Name (only for bedrijf) */}\n          {formData.type === 'bedrijf' && (\n            <div>\n              <label htmlFor=\"bedrijfsnaam\" className=\"block text-sm font-medium text-foreground mb-2\">\n                Bedrijfsnaam *\n              </label>\n              <input\n                type=\"text\"\n                id=\"bedrijfsnaam\"\n                name=\"bedrijfsnaam\"\n                value={formData.bedrijfsnaam}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                  errors.bedrijfsnaam ? 'border-destructive' : 'border-input'\n                }`}\n                placeholder=\"Bedrijfsnaam\"\n              />\n              {errors.bedrijfsnaam && (\n                <p className=\"text-destructive text-sm mt-1\">{errors.bedrijfsnaam}</p>\n              )}\n            </div>\n          )}\n\n          {/* Address Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Adresgegevens</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"md:col-span-2\">\n                <label htmlFor=\"adres\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  Adres *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"adres\"\n                  name=\"adres\"\n                  value={formData.adres}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.adres ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"Straatnaam\"\n                />\n                {errors.adres && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.adres}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"huisnummer\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  Huisnummer *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"huisnummer\"\n                  name=\"huisnummer\"\n                  value={formData.huisnummer}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.huisnummer ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"123\"\n                />\n                {errors.huisnummer && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.huisnummer}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"postcode\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  Postcode *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"postcode\"\n                  name=\"postcode\"\n                  value={formData.postcode}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.postcode ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"1234 AB\"\n                />\n                {errors.postcode && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.postcode}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"stad\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  Stad *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"stad\"\n                  name=\"stad\"\n                  value={formData.stad}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.stad ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"Amsterdam\"\n                />\n                {errors.stad && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.stad}</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Contactgegevens</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"telefoonnummer\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  Telefoonnummer *\n                </label>\n                <input\n                  type=\"tel\"\n                  id=\"telefoonnummer\"\n                  name=\"telefoonnummer\"\n                  value={formData.telefoonnummer}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.telefoonnummer ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"06-12345678\"\n                />\n                {errors.telefoonnummer && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.telefoonnummer}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\n                  E-mailadres *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring ${\n                    errors.email ? 'border-destructive' : 'border-input'\n                  }`}\n                  placeholder=\"<EMAIL>\"\n                />\n                {errors.email && (\n                  <p className=\"text-destructive text-sm mt-1\">{errors.email}</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Buttons */}\n          <div className=\"flex items-center justify-end space-x-4 pt-6 border-t border-border\">\n            <Link\n              href=\"/klanten\"\n              className=\"px-4 py-2 text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Annuleren\n            </Link>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>{isSubmitting ? 'Opslaan...' : 'Klant Opslaan'}</span>\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;;;AALA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAE1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,OAAO;QACP,YAAY;QACZ,UAAU;QACV,MAAM;QACN,gBAAgB;QAChB,OAAO;IACT;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;YAC/B,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,SAAS,IAAI,KAAK,aAAa,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YAChE,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;YAC/B,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,CAAC,wBAAwB,IAAI,CAAC,SAAS,QAAQ,GAAG;YAC3D,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,cAAc,CAAC,IAAI,IAAI;YACnC,UAAU,cAAc,GAAG;QAC7B;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,kBAAkB;YAClB,MAAM,oBAAoB,SAAS,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,wBAAwB,SAAS,WAAW;YAEnH,SAAS;gBACP,GAAG,QAAQ;gBACX,UAAU;YACZ;YAEA,2BAA2B;YAC3B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAiD;;;;;;8CAGlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM;oDAAc,CAAC;4CACpE,WAAW,CAAC,wCAAwC,EAClD,SAAS,IAAI,KAAK,gBACd,8CACA,iCACJ;;8DAEF,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAE9C,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM;oDAAU,CAAC;4CAChE,WAAW,CAAC,wCAAwC,EAClD,SAAS,IAAI,KAAK,YACd,8CACA,iCACJ;;8DAEF,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;sCAMlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiD;;;;;;sDAGrF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,QAAQ,GAAG,uBAAuB,gBACzC;4CACF,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAAiC,OAAO,QAAQ;;;;;;;;;;;;8CAIjE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAiD;;;;;;sDAGvF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU;4CACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,UAAU,GAAG,uBAAuB,gBAC3C;4CACF,aAAY;;;;;;wCAEb,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;sDAAiC,OAAO,UAAU;;;;;;;;;;;;;;;;;;wBAMpE,SAAS,IAAI,KAAK,2BACjB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAe,WAAU;8CAAiD;;;;;;8CAGzF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,YAAY;oCAC5B,UAAU;oCACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,YAAY,GAAG,uBAAuB,gBAC7C;oCACF,aAAY;;;;;;gCAEb,OAAO,YAAY,kBAClB,6LAAC;oCAAE,WAAU;8CAAiC,OAAO,YAAY;;;;;;;;;;;;sCAMvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAiD;;;;;;8DAGlF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,KAAK,GAAG,uBAAuB,gBACtC;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,KAAK;;;;;;;;;;;;sDAI9D,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAiD;;;;;;8DAGvF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,UAAU,GAAG,uBAAuB,gBAC3C;oDACF,aAAY;;;;;;gDAEb,OAAO,UAAU,kBAChB,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,UAAU;;;;;;;;;;;;;;;;;;8CAKrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAiD;;;;;;8DAGrF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,QAAQ,GAAG,uBAAuB,gBACzC;oDACF,aAAY;;;;;;gDAEb,OAAO,QAAQ,kBACd,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,QAAQ;;;;;;;;;;;;sDAIjE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAiD;;;;;;8DAGjF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,IAAI,GAAG,uBAAuB,gBACrC;oDACF,aAAY;;;;;;gDAEb,OAAO,IAAI,kBACV,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAOjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAiB,WAAU;8DAAiD;;;;;;8DAG3F,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,cAAc;oDAC9B,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,cAAc,GAAG,uBAAuB,gBAC/C;oDACF,aAAY;;;;;;gDAEb,OAAO,cAAc,kBACpB,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,cAAc;;;;;;;;;;;;sDAIvE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAiD;;;;;;8DAGlF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,KAAK,GAAG,uBAAuB,gBACtC;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAAiC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAM,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GA7XwB;;QACP,qIAAA,CAAA,YAAS;QACH,iIAAA,CAAA,SAAM;;;KAFL"}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "file": "building.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/building.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', ry: '2', key: '76otgf' }],\n  ['path', { d: 'M9 22v-4h6v4', key: 'r93iot' }],\n  ['path', { d: 'M8 6h.01', key: '1dz90k' }],\n  ['path', { d: 'M16 6h.01', key: '1x0f13' }],\n  ['path', { d: 'M12 6h.01', key: '1vi96p' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n];\n\n/**\n * @component @name Building\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNOSAyMnYtNGg2djQiIC8+CiAgPHBhdGggZD0iTTggNmguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNOCAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/building\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building = createLucideIcon('building', __iconNode);\n\nexport default Building;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC7C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "file": "save.js", "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}