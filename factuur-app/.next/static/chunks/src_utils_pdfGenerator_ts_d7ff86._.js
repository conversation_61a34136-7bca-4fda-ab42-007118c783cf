(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_utils_pdfGenerator_ts_d7ff86._.js", {

"[project]/src/utils/pdfGenerator.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_525a6e._.js",
  "static/chunks/src_utils_pdfGenerator_ts_af183c._.js",
  "static/chunks/node_modules_30c43d._.js",
  "static/chunks/src_utils_pdfGenerator_ts_bd542b._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/utils/pdfGenerator.ts [app-client] (ecmascript)");
    });
});
}}),
}]);