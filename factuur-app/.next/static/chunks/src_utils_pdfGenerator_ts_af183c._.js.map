{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/app10/factuur-app/src/utils/pdfGenerator.ts"], "sourcesContent": ["import { jsPDF } from 'jspdf';\nimport QRCode from 'qrcode';\n\ninterface FactuurData {\n  factuurnummer: string;\n  factuurdatum: Date;\n  vervaldatum: Date;\n  uitvoerdatum: Date;\n  klant: any;\n  factuurregels: any[];\n  extraKosten: any;\n  subtotaal: number;\n  kortingBedrag: number;\n  kortingPercentage: number;\n  btw21: number;\n  btw9: number;\n  totaalInclBtw: number;\n  betaalLink?: string;\n  bedrijfsInstellingen: any;\n}\n\nexport const generateProfessionalPDF = async (factuurData: FactuurData) => {\n  const pdf = new jsPDF('p', 'mm', 'a4');\n  const pageWidth = 210;\n  const pageHeight = 297;\n  \n  // Colors\n  const blueColor = [54, 96, 191]; // #3660BF\n  const lightBlueColor = [240, 243, 251];\n  const darkGrayColor = [51, 51, 51];\n  const lightGrayColor = [128, 128, 128];\n\n  // Helper function to add text with proper encoding\n  const addText = (text: string, x: number, y: number, options: any = {}) => {\n    pdf.setFont(options.font || 'helvetica', options.style || 'normal');\n    pdf.setFontSize(options.size || 10);\n    if (options.color) {\n      pdf.setTextColor(...options.color);\n    }\n    pdf.text(text, x, y, options.align ? { align: options.align } : undefined);\n  };\n\n  // Header with blue background\n  pdf.setFillColor(...blueColor);\n  pdf.rect(0, 0, pageWidth, 35, 'F');\n\n  // FACTUUR title in white\n  pdf.setTextColor(255, 255, 255);\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(24);\n  pdf.text('FACTUUR', 15, 20);\n\n  // Factuurnummer in white\n  pdf.setFontSize(12);\n  pdf.text(factuurData.factuurnummer, 15, 28);\n\n  // Company logo area (if available)\n  if (factuurData.bedrijfsInstellingen.logo) {\n    // Logo would be added here - for now we'll add company name\n    pdf.setFont('helvetica', 'bold');\n    pdf.setFontSize(14);\n    pdf.text(factuurData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 20, { align: 'right' });\n  }\n\n  // Reset text color\n  pdf.setTextColor(...darkGrayColor);\n\n  // Company details (right side)\n  let yPos = 45;\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n  \n  const companyDetails = [\n    factuurData.bedrijfsInstellingen.bedrijfsnaam,\n    factuurData.bedrijfsInstellingen.adres,\n    `${factuurData.bedrijfsInstellingen.postcode} ${factuurData.bedrijfsInstellingen.stad}`,\n    factuurData.bedrijfsInstellingen.telefoonnummer,\n    factuurData.bedrijfsInstellingen.email,\n    factuurData.bedrijfsInstellingen.website || ''\n  ].filter(Boolean);\n\n  companyDetails.forEach((detail, index) => {\n    pdf.text(detail, pageWidth - 15, yPos + (index * 4), { align: 'right' });\n  });\n\n  // Customer details (left side)\n  yPos = 45;\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(10);\n  pdf.text('Factuuradres:', 15, yPos);\n  \n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n  yPos += 6;\n\n  const customerName = factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam \n    ? factuurData.klant.bedrijfsnaam \n    : `${factuurData.klant.voornaam} ${factuurData.klant.achternaam}`;\n\n  const customerDetails = [\n    customerName,\n    factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam \n      ? `t.a.v. ${factuurData.klant.voornaam} ${factuurData.klant.achternaam}` \n      : '',\n    `${factuurData.klant.adres} ${factuurData.klant.huisnummer}`,\n    `${factuurData.klant.postcode} ${factuurData.klant.stad}`\n  ].filter(Boolean);\n\n  customerDetails.forEach((detail, index) => {\n    pdf.text(detail, 15, yPos + (index * 4));\n  });\n\n  // Invoice details box (right side)\n  const boxX = pageWidth - 80;\n  const boxY = 70;\n  const boxWidth = 65;\n  const boxHeight = 35;\n\n  // Light blue background for invoice details\n  pdf.setFillColor(...lightBlueColor);\n  pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');\n\n  // Invoice details\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n  pdf.setTextColor(...darkGrayColor);\n\n  const invoiceDetails = [\n    ['Factuurnummer:', factuurData.factuurnummer],\n    ['Factuurdatum:', factuurData.factuurdatum.toLocaleDateString('nl-NL')],\n    ['Vervaldatum:', factuurData.vervaldatum.toLocaleDateString('nl-NL')],\n    ['Uitvoerdatum:', factuurData.uitvoerdatum.toLocaleDateString('nl-NL')]\n  ];\n\n  invoiceDetails.forEach((detail, index) => {\n    pdf.text(detail[0], boxX + 2, boxY + 6 + (index * 6));\n    pdf.setFont('helvetica', 'normal');\n    pdf.text(detail[1], boxX + 25, boxY + 6 + (index * 6));\n    pdf.setFont('helvetica', 'bold');\n  });\n\n  // Table header\n  yPos = 120;\n  const tableStartY = yPos;\n  \n  // Blue header background\n  pdf.setFillColor(...blueColor);\n  pdf.rect(15, yPos, pageWidth - 30, 8, 'F');\n\n  // Table headers in white\n  pdf.setTextColor(255, 255, 255);\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n\n  const headers = ['Aantal', 'Beschrijving', 'Uren', 'Prijs', 'BTW', 'Totaal'];\n  const colWidths = [20, 80, 20, 25, 15, 25];\n  let xPos = 15;\n\n  headers.forEach((header, index) => {\n    pdf.text(header, xPos + 2, yPos + 5);\n    xPos += colWidths[index];\n  });\n\n  // Table rows\n  yPos += 8;\n  pdf.setTextColor(...darkGrayColor);\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n\n  factuurData.factuurregels.forEach((regel, index) => {\n    const rowY = yPos + (index * 6);\n    \n    // Alternating row colors\n    if (index % 2 === 1) {\n      pdf.setFillColor(248, 249, 250);\n      pdf.rect(15, rowY, pageWidth - 30, 6, 'F');\n    }\n\n    xPos = 15;\n    const rowData = [\n      regel.aantal.toString(),\n      regel.beschrijving,\n      regel.uren.toString(),\n      `€ ${regel.prijsPerUur.toFixed(2)}`,\n      '21%',\n      `€ ${regel.totaalExclBtw.toFixed(2)}`\n    ];\n\n    rowData.forEach((data, colIndex) => {\n      const align = colIndex === 1 ? 'left' : colIndex === 0 ? 'center' : 'right';\n      const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 : \n                   align === 'center' ? xPos + colWidths[colIndex] / 2 : xPos + 2;\n      \n      if (colIndex === 1 && data.length > 35) {\n        // Wrap long descriptions\n        const lines = pdf.splitTextToSize(data, colWidths[colIndex] - 4);\n        lines.forEach((line: string, lineIndex: number) => {\n          pdf.text(line, textX, rowY + 3 + (lineIndex * 3));\n        });\n      } else {\n        pdf.text(data, textX, rowY + 3, align !== 'left' ? { align } : undefined);\n      }\n      \n      xPos += colWidths[colIndex];\n    });\n  });\n\n  // Extra costs if any\n  let extraCostsY = yPos + (factuurData.factuurregels.length * 6) + 5;\n  \n  if (factuurData.extraKosten.voorrijkosten > 0 || \n      factuurData.extraKosten.spoedservice > 0 || \n      factuurData.extraKosten.parkeerkosten > 0 || \n      factuurData.extraKosten.materiaalkosten > 0) {\n    \n    const extraCosts = [\n      ['Voorrijkosten', factuurData.extraKosten.voorrijkosten],\n      ['Spoedservice', factuurData.extraKosten.spoedservice],\n      ['Parkeerkosten', factuurData.extraKosten.parkeerkosten],\n      ['Materiaalkosten', factuurData.extraKosten.materiaalkosten]\n    ].filter(([_, amount]) => amount > 0);\n\n    extraCosts.forEach(([description, amount], index) => {\n      const rowY = extraCostsY + (index * 5);\n      pdf.text(description, 95, rowY);\n      pdf.text(`€ ${amount.toFixed(2)}`, pageWidth - 17, rowY, { align: 'right' });\n    });\n\n    extraCostsY += extraCosts.length * 5 + 5;\n  }\n\n  // Totals section\n  const totalsY = Math.max(extraCostsY, yPos + (factuurData.factuurregels.length * 6) + 10);\n  const totalsX = pageWidth - 80;\n\n  // Totals background\n  pdf.setFillColor(...lightBlueColor);\n  pdf.rect(totalsX, totalsY, 65, 35, 'F');\n\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(9);\n\n  const totals = [\n    ['Subtotaal:', `€ ${factuurData.subtotaal.toFixed(2)}`],\n    ...(factuurData.kortingBedrag > 0 ? [['Korting:', `€ ${factuurData.kortingBedrag.toFixed(2)}`]] : []),\n    ['BTW 21%:', `€ ${factuurData.btw21.toFixed(2)}`],\n    ['', ''], // Empty line\n    ['Totaal incl. BTW:', `€ ${factuurData.totaalInclBtw.toFixed(2)}`]\n  ];\n\n  totals.forEach((total, index) => {\n    const isTotal = total[0] === 'Totaal incl. BTW:';\n    if (isTotal) {\n      pdf.setFont('helvetica', 'bold');\n      pdf.setFontSize(11);\n    }\n    \n    if (total[0]) {\n      pdf.text(total[0], totalsX + 2, totalsY + 6 + (index * 5));\n      pdf.text(total[1], totalsX + 63, totalsY + 6 + (index * 5), { align: 'right' });\n    }\n    \n    if (isTotal) {\n      pdf.setFont('helvetica', 'normal');\n      pdf.setFontSize(9);\n    }\n  });\n\n  // QR Code if payment link exists\n  if (factuurData.betaalLink) {\n    try {\n      const qrCodeDataUrl = await QRCode.toDataURL(factuurData.betaalLink, {\n        width: 60,\n        margin: 1\n      });\n      \n      pdf.addImage(qrCodeDataUrl, 'PNG', 15, totalsY + 10, 20, 20);\n      \n      pdf.setFontSize(8);\n      pdf.text('Scan voor online betaling', 15, totalsY + 35);\n    } catch (error) {\n      console.error('Error generating QR code:', error);\n    }\n  }\n\n  // Footer\n  const footerY = pageHeight - 40;\n  \n  // Payment information\n  pdf.setFont('helvetica', 'bold');\n  pdf.setFontSize(9);\n  pdf.text('Betalingsinformatie:', 15, footerY);\n  \n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n  \n  const paymentInfo = [\n    factuurData.bedrijfsInstellingen.ibanNummer ? `IBAN: ${factuurData.bedrijfsInstellingen.ibanNummer}` : '',\n    factuurData.bedrijfsInstellingen.btwNummer ? `BTW-nr: ${factuurData.bedrijfsInstellingen.btwNummer}` : '',\n    factuurData.bedrijfsInstellingen.kvkNummer ? `KvK-nr: ${factuurData.bedrijfsInstellingen.kvkNummer}` : ''\n  ].filter(Boolean);\n\n  paymentInfo.forEach((info, index) => {\n    pdf.text(info, 15, footerY + 5 + (index * 4));\n  });\n\n  // Terms and conditions\n  if (factuurData.bedrijfsInstellingen.betaalTekst) {\n    pdf.text(factuurData.bedrijfsInstellingen.betaalTekst, 15, footerY + 20);\n  }\n\n  return pdf;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAoBO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC,KAAK,MAAM;IACjC,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,SAAS;IACT,MAAM,YAAY;QAAC;QAAI;QAAI;KAAI,EAAE,UAAU;IAC3C,MAAM,iBAAiB;QAAC;QAAK;QAAK;KAAI;IACtC,MAAM,gBAAgB;QAAC;QAAI;QAAI;KAAG;IAClC,MAAM,iBAAiB;QAAC;QAAK;QAAK;KAAI;IAEtC,mDAAmD;IACnD,MAAM,UAAU,CAAC,MAAc,GAAW,GAAW,UAAe,CAAC,CAAC;QACpE,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,aAAa,QAAQ,KAAK,IAAI;QAC1D,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI;QAChC,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,YAAY,IAAI,QAAQ,KAAK;QACnC;QACA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,QAAQ,KAAK,GAAG;YAAE,OAAO,QAAQ,KAAK;QAAC,IAAI;IAClE;IAEA,8BAA8B;IAC9B,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,GAAG,GAAG,WAAW,IAAI;IAE9B,yBAAyB;IACzB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,WAAW,IAAI;IAExB,yBAAyB;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,YAAY,aAAa,EAAE,IAAI;IAExC,mCAAmC;IACnC,IAAI,YAAY,oBAAoB,CAAC,IAAI,EAAE;QACzC,4DAA4D;QAC5D,IAAI,OAAO,CAAC,aAAa;QACzB,IAAI,WAAW,CAAC;QAChB,IAAI,IAAI,CAAC,YAAY,oBAAoB,CAAC,YAAY,EAAE,YAAY,IAAI,IAAI;YAAE,OAAO;QAAQ;IAC/F;IAEA,mBAAmB;IACnB,IAAI,YAAY,IAAI;IAEpB,+BAA+B;IAC/B,IAAI,OAAO;IACX,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,iBAAiB;QACrB,YAAY,oBAAoB,CAAC,YAAY;QAC7C,YAAY,oBAAoB,CAAC,KAAK;QACtC,GAAG,YAAY,oBAAoB,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,oBAAoB,CAAC,IAAI,EAAE;QACvF,YAAY,oBAAoB,CAAC,cAAc;QAC/C,YAAY,oBAAoB,CAAC,KAAK;QACtC,YAAY,oBAAoB,CAAC,OAAO,IAAI;KAC7C,CAAC,MAAM,CAAC;IAET,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,IAAI,IAAI,CAAC,QAAQ,YAAY,IAAI,OAAQ,QAAQ,GAAI;YAAE,OAAO;QAAQ;IACxE;IAEA,+BAA+B;IAC/B,OAAO;IACP,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,iBAAiB,IAAI;IAE9B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,QAAQ;IAER,MAAM,eAAe,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa,YAAY,KAAK,CAAC,YAAY,GACvF,YAAY,KAAK,CAAC,YAAY,GAC9B,GAAG,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE;IAEnE,MAAM,kBAAkB;QACtB;QACA,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa,YAAY,KAAK,CAAC,YAAY,GAClE,CAAC,OAAO,EAAE,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE,GACtE;QACJ,GAAG,YAAY,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,UAAU,EAAE;QAC5D,GAAG,YAAY,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,IAAI,EAAE;KAC1D,CAAC,MAAM,CAAC;IAET,gBAAgB,OAAO,CAAC,CAAC,QAAQ;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAQ,QAAQ;IACvC;IAEA,mCAAmC;IACnC,MAAM,OAAO,YAAY;IACzB,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,YAAY;IAElB,4CAA4C;IAC5C,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,MAAM,MAAM,UAAU,WAAW;IAE1C,kBAAkB;IAClB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,YAAY,IAAI;IAEpB,MAAM,iBAAiB;QACrB;YAAC;YAAkB,YAAY,aAAa;SAAC;QAC7C;YAAC;YAAiB,YAAY,YAAY,CAAC,kBAAkB,CAAC;SAAS;QACvE;YAAC;YAAgB,YAAY,WAAW,CAAC,kBAAkB,CAAC;SAAS;QACrE;YAAC;YAAiB,YAAY,YAAY,CAAC,kBAAkB,CAAC;SAAS;KACxE;IAED,eAAe,OAAO,CAAC,CAAC,QAAQ;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,GAAG,OAAO,IAAK,QAAQ;QAClD,IAAI,OAAO,CAAC,aAAa;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,IAAK,QAAQ;QACnD,IAAI,OAAO,CAAC,aAAa;IAC3B;IAEA,eAAe;IACf,OAAO;IACP,MAAM,cAAc;IAEpB,yBAAyB;IACzB,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;IAEtC,yBAAyB;IACzB,IAAI,YAAY,CAAC,KAAK,KAAK;IAC3B,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,UAAU;QAAC;QAAU;QAAgB;QAAQ;QAAS;QAAO;KAAS;IAC5E,MAAM,YAAY;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAC1C,IAAI,OAAO;IAEX,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACvB,IAAI,IAAI,CAAC,QAAQ,OAAO,GAAG,OAAO;QAClC,QAAQ,SAAS,CAAC,MAAM;IAC1B;IAEA,aAAa;IACb,QAAQ;IACR,IAAI,YAAY,IAAI;IACpB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,YAAY,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO;QACxC,MAAM,OAAO,OAAQ,QAAQ;QAE7B,yBAAyB;QACzB,IAAI,QAAQ,MAAM,GAAG;YACnB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,MAAM,YAAY,IAAI,GAAG;QACxC;QAEA,OAAO;QACP,MAAM,UAAU;YACd,MAAM,MAAM,CAAC,QAAQ;YACrB,MAAM,YAAY;YAClB,MAAM,IAAI,CAAC,QAAQ;YACnB,CAAC,EAAE,EAAE,MAAM,WAAW,CAAC,OAAO,CAAC,IAAI;YACnC;YACA,CAAC,EAAE,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;SACtC;QAED,QAAQ,OAAO,CAAC,CAAC,MAAM;YACrB,MAAM,QAAQ,aAAa,IAAI,SAAS,aAAa,IAAI,WAAW;YACpE,MAAM,QAAQ,UAAU,UAAU,OAAO,SAAS,CAAC,SAAS,GAAG,IAClD,UAAU,WAAW,OAAO,SAAS,CAAC,SAAS,GAAG,IAAI,OAAO;YAE1E,IAAI,aAAa,KAAK,KAAK,MAAM,GAAG,IAAI;gBACtC,yBAAyB;gBACzB,MAAM,QAAQ,IAAI,eAAe,CAAC,MAAM,SAAS,CAAC,SAAS,GAAG;gBAC9D,MAAM,OAAO,CAAC,CAAC,MAAc;oBAC3B,IAAI,IAAI,CAAC,MAAM,OAAO,OAAO,IAAK,YAAY;gBAChD;YACF,OAAO;gBACL,IAAI,IAAI,CAAC,MAAM,OAAO,OAAO,GAAG,UAAU,SAAS;oBAAE;gBAAM,IAAI;YACjE;YAEA,QAAQ,SAAS,CAAC,SAAS;QAC7B;IACF;IAEA,qBAAqB;IACrB,IAAI,cAAc,OAAQ,YAAY,aAAa,CAAC,MAAM,GAAG,IAAK;IAElE,IAAI,YAAY,WAAW,CAAC,aAAa,GAAG,KACxC,YAAY,WAAW,CAAC,YAAY,GAAG,KACvC,YAAY,WAAW,CAAC,aAAa,GAAG,KACxC,YAAY,WAAW,CAAC,eAAe,GAAG,GAAG;QAE/C,MAAM,aAAa;YACjB;gBAAC;gBAAiB,YAAY,WAAW,CAAC,aAAa;aAAC;YACxD;gBAAC;gBAAgB,YAAY,WAAW,CAAC,YAAY;aAAC;YACtD;gBAAC;gBAAiB,YAAY,WAAW,CAAC,aAAa;aAAC;YACxD;gBAAC;gBAAmB,YAAY,WAAW,CAAC,eAAe;aAAC;SAC7D,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,GAAK,SAAS;QAEnC,WAAW,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,EAAE;YACzC,MAAM,OAAO,cAAe,QAAQ;YACpC,IAAI,IAAI,CAAC,aAAa,IAAI;YAC1B,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,YAAY,IAAI,MAAM;gBAAE,OAAO;YAAQ;QAC5E;QAEA,eAAe,WAAW,MAAM,GAAG,IAAI;IACzC;IAEA,iBAAiB;IACjB,MAAM,UAAU,KAAK,GAAG,CAAC,aAAa,OAAQ,YAAY,aAAa,CAAC,MAAM,GAAG,IAAK;IACtF,MAAM,UAAU,YAAY;IAE5B,oBAAoB;IACpB,IAAI,YAAY,IAAI;IACpB,IAAI,IAAI,CAAC,SAAS,SAAS,IAAI,IAAI;IAEnC,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,SAAS;QACb;YAAC;YAAc,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,OAAO,CAAC,IAAI;SAAC;WACnD,YAAY,aAAa,GAAG,IAAI;YAAC;gBAAC;gBAAY,CAAC,EAAE,EAAE,YAAY,aAAa,CAAC,OAAO,CAAC,IAAI;aAAC;SAAC,GAAG,EAAE;QACpG;YAAC;YAAY,CAAC,EAAE,EAAE,YAAY,KAAK,CAAC,OAAO,CAAC,IAAI;SAAC;QACjD;YAAC;YAAI;SAAG;QACR;YAAC;YAAqB,CAAC,EAAE,EAAE,YAAY,aAAa,CAAC,OAAO,CAAC,IAAI;SAAC;KACnE;IAED,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,MAAM,UAAU,KAAK,CAAC,EAAE,KAAK;QAC7B,IAAI,SAAS;YACX,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;QAClB;QAEA,IAAI,KAAK,CAAC,EAAE,EAAE;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,UAAU,IAAK,QAAQ;YACvD,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,IAAI,UAAU,IAAK,QAAQ,GAAI;gBAAE,OAAO;YAAQ;QAC/E;QAEA,IAAI,SAAS;YACX,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,WAAW,CAAC;QAClB;IACF;IAEA,iCAAiC;IACjC,IAAI,YAAY,UAAU,EAAE;QAC1B,IAAI;YACF,MAAM,gBAAgB,MAAM,2IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,YAAY,UAAU,EAAE;gBACnE,OAAO;gBACP,QAAQ;YACV;YAEA,IAAI,QAAQ,CAAC,eAAe,OAAO,IAAI,UAAU,IAAI,IAAI;YAEzD,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,6BAA6B,IAAI,UAAU;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,SAAS;IACT,MAAM,UAAU,aAAa;IAE7B,sBAAsB;IACtB,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,wBAAwB,IAAI;IAErC,IAAI,OAAO,CAAC,aAAa;IACzB,IAAI,WAAW,CAAC;IAEhB,MAAM,cAAc;QAClB,YAAY,oBAAoB,CAAC,UAAU,GAAG,CAAC,MAAM,EAAE,YAAY,oBAAoB,CAAC,UAAU,EAAE,GAAG;QACvG,YAAY,oBAAoB,CAAC,SAAS,GAAG,CAAC,QAAQ,EAAE,YAAY,oBAAoB,CAAC,SAAS,EAAE,GAAG;QACvG,YAAY,oBAAoB,CAAC,SAAS,GAAG,CAAC,QAAQ,EAAE,YAAY,oBAAoB,CAAC,SAAS,EAAE,GAAG;KACxG,CAAC,MAAM,CAAC;IAET,YAAY,OAAO,CAAC,CAAC,MAAM;QACzB,IAAI,IAAI,CAAC,MAAM,IAAI,UAAU,IAAK,QAAQ;IAC5C;IAEA,uBAAuB;IACvB,IAAI,YAAY,oBAAoB,CAAC,WAAW,EAAE;QAChD,IAAI,IAAI,CAAC,YAAY,oBAAoB,CAAC,WAAW,EAAE,IAAI,UAAU;IACvE;IAEA,OAAO;AACT"}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}