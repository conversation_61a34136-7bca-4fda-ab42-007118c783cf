(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_utils_pdfGenerator_ts_af183c._.js", {

"[project]/src/utils/pdfGenerator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "generateProfessionalPDF": (()=>generateProfessionalPDF)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript)");
;
;
const generateProfessionalPDF = async (factuurData)=>{
    const pdf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsPDF"]('p', 'mm', 'a4');
    const pageWidth = 210;
    const pageHeight = 297;
    // Colors
    const blueColor = [
        54,
        96,
        191
    ]; // #3660BF
    const lightBlueColor = [
        240,
        243,
        251
    ];
    const darkGrayColor = [
        51,
        51,
        51
    ];
    const lightGrayColor = [
        128,
        128,
        128
    ];
    // Helper function to add text with proper encoding
    const addText = (text, x, y, options = {})=>{
        pdf.setFont(options.font || 'helvetica', options.style || 'normal');
        pdf.setFontSize(options.size || 10);
        if (options.color) {
            pdf.setTextColor(...options.color);
        }
        pdf.text(text, x, y, options.align ? {
            align: options.align
        } : undefined);
    };
    // Header with blue background
    pdf.setFillColor(...blueColor);
    pdf.rect(0, 0, pageWidth, 35, 'F');
    // FACTUUR title in white
    pdf.setTextColor(255, 255, 255);
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(24);
    pdf.text('FACTUUR', 15, 20);
    // Factuurnummer in white
    pdf.setFontSize(12);
    pdf.text(factuurData.factuurnummer, 15, 28);
    // Company logo area (if available)
    if (factuurData.bedrijfsInstellingen.logo) {
        // Logo would be added here - for now we'll add company name
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(14);
        pdf.text(factuurData.bedrijfsInstellingen.bedrijfsnaam, pageWidth - 15, 20, {
            align: 'right'
        });
    }
    // Reset text color
    pdf.setTextColor(...darkGrayColor);
    // Company details (right side)
    let yPos = 45;
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    const companyDetails = [
        factuurData.bedrijfsInstellingen.bedrijfsnaam,
        factuurData.bedrijfsInstellingen.adres,
        `${factuurData.bedrijfsInstellingen.postcode} ${factuurData.bedrijfsInstellingen.stad}`,
        factuurData.bedrijfsInstellingen.telefoonnummer,
        factuurData.bedrijfsInstellingen.email,
        factuurData.bedrijfsInstellingen.website || ''
    ].filter(Boolean);
    companyDetails.forEach((detail, index)=>{
        pdf.text(detail, pageWidth - 15, yPos + index * 4, {
            align: 'right'
        });
    });
    // Customer details (left side)
    yPos = 45;
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(10);
    pdf.text('Factuuradres:', 15, yPos);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    yPos += 6;
    const customerName = factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam ? factuurData.klant.bedrijfsnaam : `${factuurData.klant.voornaam} ${factuurData.klant.achternaam}`;
    const customerDetails = [
        customerName,
        factuurData.klant.type === 'bedrijf' && factuurData.klant.bedrijfsnaam ? `t.a.v. ${factuurData.klant.voornaam} ${factuurData.klant.achternaam}` : '',
        `${factuurData.klant.adres} ${factuurData.klant.huisnummer}`,
        `${factuurData.klant.postcode} ${factuurData.klant.stad}`
    ].filter(Boolean);
    customerDetails.forEach((detail, index)=>{
        pdf.text(detail, 15, yPos + index * 4);
    });
    // Invoice details box (right side)
    const boxX = pageWidth - 80;
    const boxY = 70;
    const boxWidth = 65;
    const boxHeight = 35;
    // Light blue background for invoice details
    pdf.setFillColor(...lightBlueColor);
    pdf.rect(boxX, boxY, boxWidth, boxHeight, 'F');
    // Invoice details
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.setTextColor(...darkGrayColor);
    const invoiceDetails = [
        [
            'Factuurnummer:',
            factuurData.factuurnummer
        ],
        [
            'Factuurdatum:',
            factuurData.factuurdatum.toLocaleDateString('nl-NL')
        ],
        [
            'Vervaldatum:',
            factuurData.vervaldatum.toLocaleDateString('nl-NL')
        ],
        [
            'Uitvoerdatum:',
            factuurData.uitvoerdatum.toLocaleDateString('nl-NL')
        ]
    ];
    invoiceDetails.forEach((detail, index)=>{
        pdf.text(detail[0], boxX + 2, boxY + 6 + index * 6);
        pdf.setFont('helvetica', 'normal');
        pdf.text(detail[1], boxX + 25, boxY + 6 + index * 6);
        pdf.setFont('helvetica', 'bold');
    });
    // Table header
    yPos = 120;
    const tableStartY = yPos;
    // Blue header background
    pdf.setFillColor(...blueColor);
    pdf.rect(15, yPos, pageWidth - 30, 8, 'F');
    // Table headers in white
    pdf.setTextColor(255, 255, 255);
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    const headers = [
        'Aantal',
        'Beschrijving',
        'Uren',
        'Prijs',
        'BTW',
        'Totaal'
    ];
    const colWidths = [
        20,
        80,
        20,
        25,
        15,
        25
    ];
    let xPos = 15;
    headers.forEach((header, index)=>{
        pdf.text(header, xPos + 2, yPos + 5);
        xPos += colWidths[index];
    });
    // Table rows
    yPos += 8;
    pdf.setTextColor(...darkGrayColor);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    factuurData.factuurregels.forEach((regel, index)=>{
        const rowY = yPos + index * 6;
        // Alternating row colors
        if (index % 2 === 1) {
            pdf.setFillColor(248, 249, 250);
            pdf.rect(15, rowY, pageWidth - 30, 6, 'F');
        }
        xPos = 15;
        const rowData = [
            regel.aantal.toString(),
            regel.beschrijving,
            regel.uren.toString(),
            `€ ${regel.prijsPerUur.toFixed(2)}`,
            '21%',
            `€ ${regel.totaalExclBtw.toFixed(2)}`
        ];
        rowData.forEach((data, colIndex)=>{
            const align = colIndex === 1 ? 'left' : colIndex === 0 ? 'center' : 'right';
            const textX = align === 'right' ? xPos + colWidths[colIndex] - 2 : align === 'center' ? xPos + colWidths[colIndex] / 2 : xPos + 2;
            if (colIndex === 1 && data.length > 35) {
                // Wrap long descriptions
                const lines = pdf.splitTextToSize(data, colWidths[colIndex] - 4);
                lines.forEach((line, lineIndex)=>{
                    pdf.text(line, textX, rowY + 3 + lineIndex * 3);
                });
            } else {
                pdf.text(data, textX, rowY + 3, align !== 'left' ? {
                    align
                } : undefined);
            }
            xPos += colWidths[colIndex];
        });
    });
    // Extra costs if any
    let extraCostsY = yPos + factuurData.factuurregels.length * 6 + 5;
    if (factuurData.extraKosten.voorrijkosten > 0 || factuurData.extraKosten.spoedservice > 0 || factuurData.extraKosten.parkeerkosten > 0 || factuurData.extraKosten.materiaalkosten > 0) {
        const extraCosts = [
            [
                'Voorrijkosten',
                factuurData.extraKosten.voorrijkosten
            ],
            [
                'Spoedservice',
                factuurData.extraKosten.spoedservice
            ],
            [
                'Parkeerkosten',
                factuurData.extraKosten.parkeerkosten
            ],
            [
                'Materiaalkosten',
                factuurData.extraKosten.materiaalkosten
            ]
        ].filter(([_, amount])=>amount > 0);
        extraCosts.forEach(([description, amount], index)=>{
            const rowY = extraCostsY + index * 5;
            pdf.text(description, 95, rowY);
            pdf.text(`€ ${amount.toFixed(2)}`, pageWidth - 17, rowY, {
                align: 'right'
            });
        });
        extraCostsY += extraCosts.length * 5 + 5;
    }
    // Totals section
    const totalsY = Math.max(extraCostsY, yPos + factuurData.factuurregels.length * 6 + 10);
    const totalsX = pageWidth - 80;
    // Totals background
    pdf.setFillColor(...lightBlueColor);
    pdf.rect(totalsX, totalsY, 65, 35, 'F');
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);
    const totals = [
        [
            'Subtotaal:',
            `€ ${factuurData.subtotaal.toFixed(2)}`
        ],
        ...factuurData.kortingBedrag > 0 ? [
            [
                'Korting:',
                `€ ${factuurData.kortingBedrag.toFixed(2)}`
            ]
        ] : [],
        [
            'BTW 21%:',
            `€ ${factuurData.btw21.toFixed(2)}`
        ],
        [
            '',
            ''
        ],
        [
            'Totaal incl. BTW:',
            `€ ${factuurData.totaalInclBtw.toFixed(2)}`
        ]
    ];
    totals.forEach((total, index)=>{
        const isTotal = total[0] === 'Totaal incl. BTW:';
        if (isTotal) {
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(11);
        }
        if (total[0]) {
            pdf.text(total[0], totalsX + 2, totalsY + 6 + index * 5);
            pdf.text(total[1], totalsX + 63, totalsY + 6 + index * 5, {
                align: 'right'
            });
        }
        if (isTotal) {
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
        }
    });
    // QR Code if payment link exists
    if (factuurData.betaalLink) {
        try {
            const qrCodeDataUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].toDataURL(factuurData.betaalLink, {
                width: 60,
                margin: 1
            });
            pdf.addImage(qrCodeDataUrl, 'PNG', 15, totalsY + 10, 20, 20);
            pdf.setFontSize(8);
            pdf.text('Scan voor online betaling', 15, totalsY + 35);
        } catch (error) {
            console.error('Error generating QR code:', error);
        }
    }
    // Footer
    const footerY = pageHeight - 40;
    // Payment information
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.text('Betalingsinformatie:', 15, footerY);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    const paymentInfo = [
        factuurData.bedrijfsInstellingen.ibanNummer ? `IBAN: ${factuurData.bedrijfsInstellingen.ibanNummer}` : '',
        factuurData.bedrijfsInstellingen.btwNummer ? `BTW-nr: ${factuurData.bedrijfsInstellingen.btwNummer}` : '',
        factuurData.bedrijfsInstellingen.kvkNummer ? `KvK-nr: ${factuurData.bedrijfsInstellingen.kvkNummer}` : ''
    ].filter(Boolean);
    paymentInfo.forEach((info, index)=>{
        pdf.text(info, 15, footerY + 5 + index * 4);
    });
    // Terms and conditions
    if (factuurData.bedrijfsInstellingen.betaalTekst) {
        pdf.text(factuurData.bedrijfsInstellingen.betaalTekst, 15, footerY + 20);
    }
    return pdf;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_utils_pdfGenerator_ts_af183c._.js.map